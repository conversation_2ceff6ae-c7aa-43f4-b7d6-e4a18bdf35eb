import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../models/hadith.dart';
import '../providers/favorites_provider.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_icons.dart';

/// شاشة تفاصيل الحديث
class HadithDetailScreen extends StatelessWidget {
  final Hadith hadith;

  const HadithDetailScreen({
    super.key,
    required this.hadith,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'حديث رقم ${hadith.id}',
          style: GoogleFonts.amiri(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          Consumer<FavoritesProvider>(
            builder: (context, favoritesProvider, child) {
              final isFavorite = favoritesProvider.isFavorite(hadith.id);

              return IconButton(
                onPressed: () async {
                  final success = await favoritesProvider.toggleFavorite(hadith.id);

                  if (success && context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          isFavorite
                              ? 'تم إزالة الحديث من المفضلة'
                              : 'تم إضافة الحديث للمفضلة',
                          style: GoogleFonts.amiri(),
                        ),
                        duration: const Duration(seconds: 2),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  }
                },
                icon: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    isFavorite ? AppIcons.favoriteSelected : AppIcons.favoriteUnselected,
                    key: ValueKey(isFavorite),
                    color: AppIcons.getFavoriteColor(isFavorite),
                  ),
                ),
                tooltip: isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة',
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // نص الحديث
            _buildHadithTextCard(context),

            const SizedBox(height: AppConstants.largePadding),

            // معلومات الحديث
            _buildHadithInfoCard(context),

            const SizedBox(height: AppConstants.largePadding),

            // الشرح والتفسير
            _buildExplanationCard(context),

            const SizedBox(height: AppConstants.largePadding),

            // الكلمات المفتاحية
            if (hadith.keywords.isNotEmpty) _buildKeywordsCard(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHadithTextCard(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.largePadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF2E7D32)
                  : const Color(0xFFE8F5E8),
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF1B5E20)
                  : const Color(0xFFEDF7ED),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  AppIcons.hadith,
                  color: AppIcons.getIconColor(context, isSelected: true),
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'نص الحديث الشريف',
                  style: GoogleFonts.amiri(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFF81C784)
                        : const Color(0xFF388E3C),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              hadith.arabicText,
              style: GoogleFonts.amiri(
                fontSize: 20,
                height: 2.0,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
              textAlign: TextAlign.right,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHadithInfoCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الحديث',
              style: GoogleFonts.amiri(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.titleLarge?.color,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildInfoRow(context, 'الراوي', hadith.narrator, AppIcons.narrator),
            const SizedBox(height: AppConstants.smallPadding),
            _buildInfoRow(context, 'المصدر', hadith.source, AppIcons.source),
            const SizedBox(height: AppConstants.smallPadding),
            _buildInfoRow(context, 'التصنيف', hadith.category, AppIcons.category),
            if (hadith.isAuthentic) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Row(
                children: [
                  Icon(
                    AppIcons.authentic,
                    color: AppIcons.getAuthenticColor(context),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'حديث صحيح',
                    style: GoogleFonts.amiri(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 18,
          color: AppIcons.getIconColor(context, isSelected: true),
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: GoogleFonts.amiri(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.titleMedium?.color,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: GoogleFonts.amiri(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExplanationCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  AppIcons.explanation,
                  color: AppIcons.getIconColor(context, isSelected: true),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'الشرح والتفسير',
                  style: GoogleFonts.amiri(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.titleLarge?.color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              hadith.explanation,
              style: GoogleFonts.amiri(
                fontSize: 16,
                height: 1.8,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKeywordsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الكلمات المفتاحية',
              style: GoogleFonts.amiri(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.titleMedium?.color,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: hadith.keywords.map((keyword) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFF2E7D32)
                        : const Color(0xFFE8F5E8),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    keyword,
                    style: GoogleFonts.amiri(
                      fontSize: 12,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFF81C784)
                          : const Color(0xFF2E7D32),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}
