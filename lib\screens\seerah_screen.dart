import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';

/// شاشة السيرة النبوية
class SeerahScreen extends StatelessWidget {
  const SeerahScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مقدمة السيرة
            _buildIntroCard(context),
            
            const SizedBox(height: AppConstants.largePadding),

            // عنوان المراحل
            Text(
              'مراحل حياة النبي ﷺ',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // قائمة المراحل
            ..._buildSeerahStages(context),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroCard(BuildContext context) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.largePadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              AppColors.seerahSectionColor.withOpacity(0.1),
              AppColors.seerahSectionColor.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_stories,
                  color: AppColors.seerahSectionColor,
                  size: 28,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'السيرة النبوية الشريفة',
                  style: GoogleFonts.amiri(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: AppColors.seerahSectionColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'تعرف على حياة النبي محمد صلى الله عليه وسلم من المولد إلى الوفاة، واستكشف المراحل المهمة في حياته الشريفة والدروس المستفادة من كل مرحلة.',
              style: GoogleFonts.amiri(
                fontSize: 16,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildSeerahStages(BuildContext context) {
    final stages = [
      {
        'title': 'المولد والطفولة',
        'subtitle': 'ولادة النبي ﷺ ونشأته في مكة',
        'description': 'ولد النبي محمد صلى الله عليه وسلم في مكة المكرمة في عام الفيل...',
        'icon': Icons.child_care,
      },
      {
        'title': 'الشباب والتجارة',
        'subtitle': 'حياة النبي ﷺ قبل البعثة',
        'description': 'عُرف النبي ﷺ بالصادق الأمين قبل البعثة...',
        'icon': Icons.business,
      },
      {
        'title': 'البعثة والنبوة',
        'subtitle': 'نزول الوحي في غار حراء',
        'description': 'بدأت النبوة بنزول جبريل عليه السلام في غار حراء...',
        'icon': Icons.lightbulb,
      },
      {
        'title': 'الدعوة في مكة',
        'subtitle': 'الدعوة السرية والجهرية',
        'description': 'بدأ النبي ﷺ بالدعوة سراً ثم جهراً...',
        'icon': Icons.campaign,
      },
      {
        'title': 'الهجرة إلى المدينة',
        'subtitle': 'الانتقال من مكة إلى المدينة',
        'description': 'هاجر النبي ﷺ من مكة إلى المدينة المنورة...',
        'icon': Icons.directions_walk,
      },
      {
        'title': 'بناء الدولة الإسلامية',
        'subtitle': 'تأسيس المجتمع الإسلامي في المدينة',
        'description': 'بنى النبي ﷺ المجتمع الإسلامي الأول...',
        'icon': Icons.account_balance,
      },
      {
        'title': 'الوفاة والانتقال',
        'subtitle': 'وفاة النبي ﷺ والرفيق الأعلى',
        'description': 'انتقل النبي ﷺ إلى الرفيق الأعلى...',
        'icon': Icons.star,
      },
    ];

    return stages.map((stage) => _buildStageCard(context, stage)).toList();
  }

  Widget _buildStageCard(BuildContext context, Map<String, dynamic> stage) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Card(
        child: InkWell(
          onTap: () => _showStageDetails(context, stage),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                // الأيقونة
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.seerahSectionColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    stage['icon'] as IconData,
                    color: AppColors.seerahSectionColor,
                    size: 24,
                  ),
                ),

                const SizedBox(width: AppConstants.defaultPadding),

                // المحتوى
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        stage['title'] as String,
                        style: GoogleFonts.amiri(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        stage['subtitle'] as String,
                        style: GoogleFonts.amiri(
                          fontSize: 14,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                      ),
                    ],
                  ),
                ),

                // سهم الانتقال
                Icon(
                  Icons.arrow_forward_ios,
                  color: Theme.of(context).iconTheme.color,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showStageDetails(BuildContext context, Map<String, dynamic> stage) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(AppConstants.largePadding),
          ),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // المحتوى
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.largePadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // العنوان
                    Row(
                      children: [
                        Icon(
                          stage['icon'] as IconData,
                          color: AppColors.seerahSectionColor,
                          size: 28,
                        ),
                        const SizedBox(width: AppConstants.smallPadding),
                        Expanded(
                          child: Text(
                            stage['title'] as String,
                            style: GoogleFonts.amiri(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // العنوان الفرعي
                    Text(
                      stage['subtitle'] as String,
                      style: GoogleFonts.amiri(
                        fontSize: 16,
                        color: AppColors.seerahSectionColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),

                    const SizedBox(height: AppConstants.largePadding),

                    // الوصف
                    Expanded(
                      child: SingleChildScrollView(
                        child: Text(
                          stage['description'] as String,
                          style: GoogleFonts.amiri(
                            fontSize: 16,
                            height: 1.8,
                          ),
                        ),
                      ),
                    ),

                    // زر الإغلاق
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'إغلاق',
                          style: GoogleFonts.amiri(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
