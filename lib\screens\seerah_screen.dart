import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';
import '../core/utils/color_utils.dart';
import '../data/seerah_data.dart';
import '../models/seerah_stage.dart';

/// شاشة السيرة النبوية
class SeerahScreen extends StatelessWidget {
  const SeerahScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مقدمة السيرة
            _buildIntroCard(context),

            const SizedBox(height: AppConstants.largePadding),

            // عنوان المراحل
            Text(
              'مراحل حياة النبي ﷺ',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // قائمة المراحل
            ..._buildSeerahStages(context),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroCard(BuildContext context) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.largePadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: AppColors.seerahGradient.map((color) =>
              ColorUtils.withAlpha(color, 0.1)).toList(),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_stories,
                  color: AppColors.seerahSectionColor,
                  size: 28,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'السيرة النبوية الشريفة',
                  style: GoogleFonts.amiri(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: AppColors.seerahSectionColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'تعرف على حياة النبي محمد صلى الله عليه وسلم من المولد إلى الوفاة، واستكشف المراحل المهمة في حياته الشريفة والدروس المستفادة من كل مرحلة.',
              style: GoogleFonts.amiri(
                fontSize: 16,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildSeerahStages(BuildContext context) {
    final stages = SeerahData.getSeerahStages();
    return stages.map((stage) => _buildStageCard(context, stage)).toList();
  }

  Widget _buildStageCard(BuildContext context, SeerahStage stage) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Card(
        child: InkWell(
          onTap: () => _showStageDetails(context, stage),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                // الأيقونة
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: ColorUtils.withLowAlpha(AppColors.seerahSectionColor),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getStageIcon(stage.id),
                    color: AppColors.seerahSectionColor,
                    size: 24,
                  ),
                ),

                const SizedBox(width: AppConstants.defaultPadding),

                // المحتوى
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        stage.title,
                        style: GoogleFonts.amiri(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        stage.subtitle,
                        style: GoogleFonts.amiri(
                          fontSize: 14,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Theme.of(context).textTheme.bodySmall?.color
                              : Colors.grey[600], // لون أغمق للوضع النهاري
                        ),
                      ),
                    ],
                  ),
                ),

                // سهم الانتقال
                Icon(
                  Icons.arrow_forward_ios,
                  color: Theme.of(context).iconTheme.color,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getStageIcon(String stageId) {
    switch (stageId) {
      case '1':
        return Icons.child_care;
      case '2':
        return Icons.business;
      case '3':
        return Icons.lightbulb;
      case '4':
        return Icons.campaign;
      case '5':
        return Icons.directions_walk;
      case '6':
        return Icons.account_balance;
      case '7':
        return Icons.star;
      default:
        return Icons.book;
    }
  }

  void _showStageDetails(BuildContext context, SeerahStage stage) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(AppConstants.largePadding),
          ),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // المحتوى
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.largePadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // العنوان
                    Row(
                      children: [
                        Icon(
                          _getStageIcon(stage.id),
                          color: AppColors.seerahSectionColor,
                          size: 28,
                        ),
                        const SizedBox(width: AppConstants.smallPadding),
                        Expanded(
                          child: Text(
                            stage.title,
                            style: GoogleFonts.amiri(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // العنوان الفرعي
                    Text(
                      stage.subtitle,
                      style: GoogleFonts.amiri(
                        fontSize: 16,
                        color: AppColors.seerahSectionColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),

                    const SizedBox(height: AppConstants.largePadding),

                    // الوصف التفصيلي
                    Text(
                      stage.detailedContent,
                      style: GoogleFonts.amiri(
                        fontSize: 16,
                        height: 1.8,
                        color: Theme.of(context).textTheme.bodyLarge?.color,
                      ),
                    ),

                    const SizedBox(height: AppConstants.largePadding),

                    // زر الإغلاق
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'إغلاق',
                          style: GoogleFonts.amiri(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
