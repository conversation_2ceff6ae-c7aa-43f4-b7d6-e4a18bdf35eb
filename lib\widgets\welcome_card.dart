import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../core/constants/app_constants.dart';
import '../core/utils/color_utils.dart';


/// بطاقة الترحيب في الصفحة الرئيسية
class WelcomeCard extends StatelessWidget {
  const WelcomeCard({super.key});

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final hour = now.hour;

    String greeting;
    IconData greetingIcon;

    if (hour < 12) {
      greeting = 'صباح الخير';
      greetingIcon = Icons.wb_sunny;
    } else if (hour < 17) {
      greeting = 'مساء الخير';
      greetingIcon = Icons.wb_sunny_outlined;
    } else {
      greeting = 'مساء الخير';
      greetingIcon = Icons.nights_stay;
    }

    return Card(
      elevation: AppConstants.cardElevation,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.largePadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              Theme.of(context).primaryColor,
              ColorUtils.withAlpha(Theme.of(context).primaryColor, 0.8),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // التحية
            Row(
              children: [
                Icon(
                  greetingIcon,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  greeting,
                  style: GoogleFonts.amiri(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // الرسالة الرئيسية
            Text(
              'أهلاً وسهلاً بك في تطبيق سيرة النبي ﷺ',
              style: GoogleFonts.amiri(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                height: 1.4,
              ),
            ),

            const SizedBox(height: AppConstants.smallPadding),

            // الوصف
            Text(
              'استكشف حياة النبي محمد صلى الله عليه وسلم وتعلم من سيرته العطرة',
              style: GoogleFonts.amiri(
                fontSize: 16,
                color: ColorUtils.withAlpha(Colors.white, 0.9),
                height: 1.5,
              ),
            ),

            const SizedBox(height: AppConstants.largePadding),

            // إحصائية سريعة أو دعوة للعمل
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: AppConstants.smallPadding,
              ),
              decoration: BoxDecoration(
                color: ColorUtils.withAlpha(Colors.white, 0.2),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.auto_stories,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Text(
                    'ابدأ رحلتك في تعلم السيرة النبوية',
                    style: GoogleFonts.amiri(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
