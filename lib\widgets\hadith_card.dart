import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../models/hadith.dart';
import '../providers/favorites_provider.dart';
import '../core/constants/app_constants.dart';

/// بطاقة الحديث مع زر المفضلة
class HadithCard extends StatelessWidget {
  final Hadith hadith;
  final VoidCallback? onTap;
  final bool showFavoriteButton;

  const HadithCard({
    super.key,
    required this.hadith,
    this.onTap,
    this.showFavoriteButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: AppConstants.smallPadding,
      ),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة مع زر المفضلة
              Row(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          Icons.format_quote,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFF81C784)
                              : const Color(0xFF388E3C),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'حديث رقم ${hadith.id}',
                            style: GoogleFonts.amiri(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFF81C784)
                                  : const Color(0xFF388E3C),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (showFavoriteButton) _buildFavoriteButton(context),
                ],
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // نص الحديث
              Text(
                hadith.arabicText,
                style: GoogleFonts.amiri(
                  fontSize: 16,
                  height: 1.8,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                ),
                textAlign: TextAlign.right,
              ),
              
              const SizedBox(height: AppConstants.smallPadding),
              
              // الراوي والمصدر
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'الراوي: ${hadith.narrator}',
                      style: GoogleFonts.amiri(
                        fontSize: 12,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).textTheme.bodySmall?.color
                            : Colors.grey[700],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 4),
              
              Row(
                children: [
                  Icon(
                    Icons.book,
                    size: 14,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFF81C784)
                        : const Color(0xFF388E3C),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    hadith.source,
                    style: GoogleFonts.amiri(
                      fontSize: 12,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFF81C784)
                          : const Color(0xFF388E3C),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  if (hadith.isAuthentic)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFF2E7D32)
                            : const Color(0xFFE8F5E8),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'صحيح',
                        style: GoogleFonts.amiri(
                          fontSize: 10,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFF81C784)
                              : const Color(0xFF2E7D32),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFavoriteButton(BuildContext context) {
    return Consumer<FavoritesProvider>(
      builder: (context, favoritesProvider, child) {
        final isFavorite = favoritesProvider.isFavorite(hadith.id);
        
        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          child: IconButton(
            onPressed: () async {
              final success = await favoritesProvider.toggleFavorite(hadith.id);
              
              if (success && context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      isFavorite 
                          ? 'تم إزالة الحديث من المفضلة'
                          : 'تم إضافة الحديث للمفضلة',
                      style: GoogleFonts.amiri(),
                    ),
                    duration: const Duration(seconds: 1),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
            icon: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                key: ValueKey(isFavorite),
                color: isFavorite 
                    ? Colors.red[400]
                    : Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey[400]
                        : Colors.grey[600],
                size: 22,
              ),
            ),
            tooltip: isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة',
          ),
        );
      },
    );
  }
}
