/// ثوابت التطبيق الأساسية
class AppConstants {
  // معلومات التطبيق
  static const String appName = 'سيرة النبي محمد ﷺ';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق تعليمي ديني لسيرة النبي محمد صلى الله عليه وسلم';

  // أسماء الأقسام
  static const String homeSection = 'الرئيسية';
  static const String seerahSection = 'السيرة النبوية';
  static const String hadithSection = 'الأحاديث النبوية';
  static const String placesSection = 'الأماكن المقدسة';
  static const String aboutSection = 'حول التطبيق';

  // مفاتيح التخزين المحلي
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language_code';
  static const String firstLaunchKey = 'first_launch';

  // أبعاد التصميم
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;

  // مدة الرسوم المتحركة
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 3);

  // أحجام الخطوط
  static const double titleFontSize = 24.0;
  static const double subtitleFontSize = 18.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 14.0;

  // أسماء الخطوط
  static const String arabicFontFamily = 'Amiri';
  static const String englishFontFamily = 'Roboto';
}
