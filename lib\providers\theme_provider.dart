import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/constants/app_constants.dart';

/// مزود إدارة الثيم (النهاري/الليلي)
class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.light;
  late SharedPreferences _prefs;

  ThemeMode get themeMode => _themeMode;

  bool get isDarkMode => _themeMode == ThemeMode.dark;

  /// تهيئة المزود وتحميل الإعدادات المحفوظة
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadThemeMode();
  }

  /// تحميل وضع الثيم المحفوظ
  Future<void> _loadThemeMode() async {
    final savedThemeMode = _prefs.getString(AppConstants.themeKey);
    
    if (savedThemeMode != null) {
      switch (savedThemeMode) {
        case 'light':
          _themeMode = ThemeMode.light;
          break;
        case 'dark':
          _themeMode = ThemeMode.dark;
          break;
        case 'system':
          _themeMode = ThemeMode.system;
          break;
        default:
          _themeMode = ThemeMode.light;
      }
    } else {
      // إذا لم يكن هناك إعداد محفوظ، استخدم الوضع النهاري كافتراضي
      _themeMode = ThemeMode.light;
    }
    
    notifyListeners();
  }

  /// حفظ وضع الثيم
  Future<void> _saveThemeMode() async {
    String themeModeString;
    switch (_themeMode) {
      case ThemeMode.light:
        themeModeString = 'light';
        break;
      case ThemeMode.dark:
        themeModeString = 'dark';
        break;
      case ThemeMode.system:
        themeModeString = 'system';
        break;
    }
    
    await _prefs.setString(AppConstants.themeKey, themeModeString);
  }

  /// تغيير إلى الوضع النهاري
  Future<void> setLightMode() async {
    _themeMode = ThemeMode.light;
    await _saveThemeMode();
    notifyListeners();
  }

  /// تغيير إلى الوضع الليلي
  Future<void> setDarkMode() async {
    _themeMode = ThemeMode.dark;
    await _saveThemeMode();
    notifyListeners();
  }

  /// تغيير إلى وضع النظام
  Future<void> setSystemMode() async {
    _themeMode = ThemeMode.system;
    await _saveThemeMode();
    notifyListeners();
  }

  /// تبديل بين الوضع النهاري والليلي
  Future<void> toggleTheme() async {
    if (_themeMode == ThemeMode.light) {
      await setDarkMode();
    } else {
      await setLightMode();
    }
  }

  /// الحصول على اسم الوضع الحالي
  String get currentThemeName {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'الوضع النهاري';
      case ThemeMode.dark:
        return 'الوضع الليلي';
      case ThemeMode.system:
        return 'وضع النظام';
    }
  }

  /// الحصول على أيقونة الوضع الحالي
  IconData get currentThemeIcon {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }
}
