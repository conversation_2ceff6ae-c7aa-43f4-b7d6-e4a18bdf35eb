import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';
import '../core/utils/color_utils.dart';
import '../data/hadith_data.dart';
import '../models/hadith.dart';

/// شاشة الأحاديث النبوية
class HadithScreen extends StatelessWidget {
  const HadithScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مقدمة الأحاديث
            _buildIntroCard(context),

            const SizedBox(height: AppConstants.largePadding),

            // عنوان الأحاديث
            Text(
              'مختارات من الأحاديث النبوية',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // قائمة الأحاديث
            ..._buildHadithList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroCard(BuildContext context) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.largePadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: AppColors.hadithGradient.map((color) =>
              ColorUtils.withAlpha(color, 0.1)).toList(),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.format_quote,
                  color: AppColors.hadithSectionColor,
                  size: 28,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'الأحاديث النبوية الشريفة',
                  style: GoogleFonts.amiri(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: AppColors.hadithSectionColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'مجموعة مختارة من أحاديث النبي محمد صلى الله عليه وسلم مع شروحات مبسطة لفهم معانيها والاستفادة من حكمها.',
              style: GoogleFonts.amiri(
                fontSize: 16,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildHadithList(BuildContext context) {
    final hadiths = HadithData.getHadiths();
    return hadiths.map((hadith) => _buildHadithCard(context, hadith)).toList();
  }

  Widget _buildHadithCard(BuildContext context, Hadith hadith) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Card(
        child: ExpansionTile(
          tilePadding: const EdgeInsets.all(AppConstants.defaultPadding),
          childrenPadding: const EdgeInsets.all(AppConstants.defaultPadding),
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: ColorUtils.withLowAlpha(AppColors.hadithSectionColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.format_quote,
              color: AppColors.hadithSectionColor,
              size: 20,
            ),
          ),
          title: Text(
            hadith.arabicText,
            style: GoogleFonts.amiri(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              height: 1.8,
            ),
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: AppConstants.smallPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'رواه: ${hadith.narrator}',
                  style: GoogleFonts.amiri(
                    fontSize: 14,
                    color: AppColors.hadithSectionColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  'المصدر: ${hadith.source}',
                  style: GoogleFonts.amiri(
                    fontSize: 12,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ),
              ],
            ),
          ),
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: ColorUtils.withAlpha(AppColors.hadithSectionColor, 0.05),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: AppColors.hadithSectionColor,
                        size: 20,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      Text(
                        'الشرح والفائدة',
                        style: GoogleFonts.amiri(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.hadithSectionColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    hadith.explanation,
                    style: GoogleFonts.amiri(
                      fontSize: 15,
                      height: 1.7,
                    ),
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.defaultPadding,
                      vertical: AppConstants.smallPadding,
                    ),
                    decoration: BoxDecoration(
                      color: ColorUtils.withLowAlpha(AppColors.hadithSectionColor),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'التصنيف: ${hadith.category}',
                      style: GoogleFonts.amiri(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppColors.hadithSectionColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
