import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_icons.dart';
import '../core/utils/color_utils.dart';
import '../data/hadith_data.dart';
import '../data/additional_hadith_data.dart';
import '../data/more_hadith_data.dart';
import '../data/final_hadith_data.dart';
import '../models/hadith.dart';
import '../widgets/hadith_card.dart';
import '../providers/favorites_provider.dart';
import 'hadith_detail_screen.dart';
import 'favorites_screen.dart';

/// شاشة الأحاديث النبوية
class HadithScreen extends StatefulWidget {
  const HadithScreen({super.key});

  @override
  State<HadithScreen> createState() => _HadithScreenState();
}

class _HadithScreenState extends State<HadithScreen> {
  List<Hadith> _allHadiths = [];

  @override
  void initState() {
    super.initState();
    _loadAllHadiths();
  }

  void _loadAllHadiths() {
    _allHadiths = [
      ...HadithData.getHadiths(),
      ...AdditionalHadithData.getAdditionalHadiths(),
      ...MoreHadithData.getMoreHadiths(),
      ...FinalHadithData.getFinalHadiths(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الأحاديث النبوية',
          style: GoogleFonts.amiri(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          Consumer<FavoritesProvider>(
            builder: (context, favoritesProvider, child) {
              return Stack(
                children: [
                  IconButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const FavoritesScreen(),
                        ),
                      );
                    },
                    icon: Icon(AppIcons.favoritesList),
                    tooltip: 'المفضلة',
                  ),
                  if (favoritesProvider.favoritesCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '${favoritesProvider.favoritesCount}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مقدمة الأحاديث
            _buildIntroCard(context),

            const SizedBox(height: AppConstants.largePadding),

            // عنوان الأحاديث
            Text(
              'مجموعة شاملة من الأحاديث النبوية (${_allHadiths.length} حديث)',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // قائمة الأحاديث
            ..._buildHadithList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroCard(BuildContext context) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.largePadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: AppColors.hadithGradient.map((color) =>
              ColorUtils.withAlpha(color, 0.1)).toList(),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.format_quote,
                  color: AppColors.hadithSectionColor,
                  size: 28,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'الأحاديث النبوية الشريفة',
                  style: GoogleFonts.amiri(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: AppColors.hadithSectionColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'مجموعة مختارة من أحاديث النبي محمد صلى الله عليه وسلم مع شروحات مبسطة لفهم معانيها والاستفادة من حكمها.',
              style: GoogleFonts.amiri(
                fontSize: 16,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildHadithList(BuildContext context) {
    return _allHadiths.map((hadith) =>
      HadithCard(
        hadith: hadith,
        onTap: () => _navigateToHadithDetail(hadith),
      )
    ).toList();
  }

  void _navigateToHadithDetail(Hadith hadith) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithDetailScreen(hadith: hadith),
      ),
    );
  }

}
