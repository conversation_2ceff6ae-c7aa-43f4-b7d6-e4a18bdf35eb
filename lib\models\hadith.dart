/// نموذج الحديث النبوي
class Hadith {
  final String id;
  final String arabicText;
  final String narrator;
  final String source;
  final String explanation;
  final String category;
  final List<String> keywords;
  final bool isAuthentic;

  const Had<PERSON>({
    required this.id,
    required this.arabicText,
    required this.narrator,
    required this.source,
    required this.explanation,
    required this.category,
    required this.keywords,
    this.isAuthentic = true,
  });

  factory Hadith.fromJson(Map<String, dynamic> json) {
    return Hadith(
      id: json['id'] as String,
      arabicText: json['arabicText'] as String,
      narrator: json['narrator'] as String,
      source: json['source'] as String,
      explanation: json['explanation'] as String,
      category: json['category'] as String,
      keywords: List<String>.from(json['keywords'] as List),
      isAuthentic: json['isAuthentic'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabicText': arabicText,
      'narrator': narrator,
      'source': source,
      'explanation': explanation,
      'category': category,
      'keywords': keywords,
      'isAuthentic': isAuthentic,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Hadith && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Hadith(id: $id, narrator: $narrator)';
  }
}
