import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_colors.dart';
import '../constants/app_constants.dart';

/// إعدادات ثيم التطبيق
class AppTheme {
  // الثيم النهاري
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      
      // الألوان الأساسية
      colorScheme: const ColorScheme.light(
        primary: AppColors.primaryColor,
        primaryContainer: AppColors.primaryLightColor,
        secondary: AppColors.secondaryColor,
        secondaryContainer: AppColors.secondaryLightColor,
        surface: AppColors.lightSurface,
        background: AppColors.lightBackground,
        error: AppColors.errorColor,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: AppColors.lightTextPrimary,
        onBackground: AppColors.lightTextPrimary,
        onError: Colors.white,
      ),

      // خلفية التطبيق
      scaffoldBackgroundColor: AppColors.lightBackground,

      // شريط التطبيق
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.amiri(
          fontSize: AppConstants.titleFontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),

      // البطاقات
      cardTheme: const CardTheme(
        color: AppColors.lightCardBackground,
        elevation: AppConstants.cardElevation,
        shadowColor: AppColors.shadowColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(AppConstants.borderRadius),
          ),
        ),
      ),

      // الأزرار المرفوعة
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.largePadding,
            vertical: AppConstants.defaultPadding,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
          textStyle: GoogleFonts.amiri(
            fontSize: AppConstants.bodyFontSize,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // الأزرار النصية
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primaryColor,
          textStyle: GoogleFonts.amiri(
            fontSize: AppConstants.bodyFontSize,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.lightSurface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          borderSide: const BorderSide(color: AppColors.primaryColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          borderSide: BorderSide(color: AppColors.primaryColor.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          borderSide: const BorderSide(color: AppColors.primaryColor, width: 2),
        ),
        labelStyle: GoogleFonts.amiri(
          color: AppColors.lightTextSecondary,
          fontSize: AppConstants.bodyFontSize,
        ),
        hintStyle: GoogleFonts.amiri(
          color: AppColors.lightTextHint,
          fontSize: AppConstants.bodyFontSize,
        ),
      ),

      // النصوص
      textTheme: TextTheme(
        displayLarge: GoogleFonts.amiri(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: AppColors.lightTextPrimary,
        ),
        displayMedium: GoogleFonts.amiri(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: AppColors.lightTextPrimary,
        ),
        displaySmall: GoogleFonts.amiri(
          fontSize: AppConstants.titleFontSize,
          fontWeight: FontWeight.bold,
          color: AppColors.lightTextPrimary,
        ),
        headlineLarge: GoogleFonts.amiri(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: AppColors.lightTextPrimary,
        ),
        headlineMedium: GoogleFonts.amiri(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: AppColors.lightTextPrimary,
        ),
        headlineSmall: GoogleFonts.amiri(
          fontSize: AppConstants.subtitleFontSize,
          fontWeight: FontWeight.w600,
          color: AppColors.lightTextPrimary,
        ),
        titleLarge: GoogleFonts.amiri(
          fontSize: AppConstants.bodyFontSize,
          fontWeight: FontWeight.w600,
          color: AppColors.lightTextPrimary,
        ),
        titleMedium: GoogleFonts.amiri(
          fontSize: AppConstants.bodyFontSize,
          fontWeight: FontWeight.w500,
          color: AppColors.lightTextPrimary,
        ),
        titleSmall: GoogleFonts.amiri(
          fontSize: AppConstants.captionFontSize,
          fontWeight: FontWeight.w500,
          color: AppColors.lightTextSecondary,
        ),
        bodyLarge: GoogleFonts.amiri(
          fontSize: AppConstants.bodyFontSize,
          color: AppColors.lightTextPrimary,
          height: 1.6,
        ),
        bodyMedium: GoogleFonts.amiri(
          fontSize: AppConstants.captionFontSize,
          color: AppColors.lightTextSecondary,
          height: 1.5,
        ),
        bodySmall: GoogleFonts.amiri(
          fontSize: 12,
          color: AppColors.lightTextHint,
          height: 1.4,
        ),
      ),

      // شريط التبويب السفلي
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.lightSurface,
        selectedItemColor: AppColors.primaryColor,
        unselectedItemColor: AppColors.lightTextSecondary,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // الدرج الجانبي
      drawerTheme: const DrawerThemeData(
        backgroundColor: AppColors.lightSurface,
        elevation: 16,
      ),

      // أيقونات
      iconTheme: const IconThemeData(
        color: AppColors.lightTextSecondary,
        size: 24,
      ),

      // الفواصل
      dividerTheme: const DividerThemeData(
        color: AppColors.lightTextHint,
        thickness: 1,
        space: 1,
      ),
    );
  }

  // الثيم الليلي
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      
      // الألوان الأساسية
      colorScheme: const ColorScheme.dark(
        primary: AppColors.primaryLightColor,
        primaryContainer: AppColors.primaryColor,
        secondary: AppColors.secondaryLightColor,
        secondaryContainer: AppColors.secondaryColor,
        surface: AppColors.darkSurface,
        background: AppColors.darkBackground,
        error: AppColors.errorColor,
        onPrimary: Colors.black,
        onSecondary: Colors.black,
        onSurface: AppColors.darkTextPrimary,
        onBackground: AppColors.darkTextPrimary,
        onError: Colors.white,
      ),

      // خلفية التطبيق
      scaffoldBackgroundColor: AppColors.darkBackground,

      // شريط التطبيق
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.darkSurface,
        foregroundColor: AppColors.darkTextPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.amiri(
          fontSize: AppConstants.titleFontSize,
          fontWeight: FontWeight.bold,
          color: AppColors.darkTextPrimary,
        ),
      ),

      // البطاقات
      cardTheme: const CardTheme(
        color: AppColors.darkCardBackground,
        elevation: AppConstants.cardElevation,
        shadowColor: AppColors.shadowColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(AppConstants.borderRadius),
          ),
        ),
      ),

      // النصوص
      textTheme: TextTheme(
        displayLarge: GoogleFonts.amiri(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: AppColors.darkTextPrimary,
        ),
        displayMedium: GoogleFonts.amiri(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: AppColors.darkTextPrimary,
        ),
        displaySmall: GoogleFonts.amiri(
          fontSize: AppConstants.titleFontSize,
          fontWeight: FontWeight.bold,
          color: AppColors.darkTextPrimary,
        ),
        headlineLarge: GoogleFonts.amiri(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: AppColors.darkTextPrimary,
        ),
        headlineMedium: GoogleFonts.amiri(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: AppColors.darkTextPrimary,
        ),
        headlineSmall: GoogleFonts.amiri(
          fontSize: AppConstants.subtitleFontSize,
          fontWeight: FontWeight.w600,
          color: AppColors.darkTextPrimary,
        ),
        titleLarge: GoogleFonts.amiri(
          fontSize: AppConstants.bodyFontSize,
          fontWeight: FontWeight.w600,
          color: AppColors.darkTextPrimary,
        ),
        titleMedium: GoogleFonts.amiri(
          fontSize: AppConstants.bodyFontSize,
          fontWeight: FontWeight.w500,
          color: AppColors.darkTextPrimary,
        ),
        titleSmall: GoogleFonts.amiri(
          fontSize: AppConstants.captionFontSize,
          fontWeight: FontWeight.w500,
          color: AppColors.darkTextSecondary,
        ),
        bodyLarge: GoogleFonts.amiri(
          fontSize: AppConstants.bodyFontSize,
          color: AppColors.darkTextPrimary,
          height: 1.6,
        ),
        bodyMedium: GoogleFonts.amiri(
          fontSize: AppConstants.captionFontSize,
          color: AppColors.darkTextSecondary,
          height: 1.5,
        ),
        bodySmall: GoogleFonts.amiri(
          fontSize: 12,
          color: AppColors.darkTextHint,
          height: 1.4,
        ),
      ),

      // شريط التبويب السفلي
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.darkSurface,
        selectedItemColor: AppColors.primaryLightColor,
        unselectedItemColor: AppColors.darkTextSecondary,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // الدرج الجانبي
      drawerTheme: const DrawerThemeData(
        backgroundColor: AppColors.darkSurface,
        elevation: 16,
      ),

      // أيقونات
      iconTheme: const IconThemeData(
        color: AppColors.darkTextSecondary,
        size: 24,
      ),

      // الفواصل
      dividerTheme: const DividerThemeData(
        color: AppColors.darkTextHint,
        thickness: 1,
        space: 1,
      ),
    );
  }
}
