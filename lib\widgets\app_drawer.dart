import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';
import '../core/utils/color_utils.dart';
import '../providers/theme_provider.dart';

/// الدرج الجانبي للتطبيق
class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // رأس الدرج
          _buildDrawerHeader(context),

          // قائمة العناصر
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  context,
                  icon: Icons.home,
                  title: 'الصفحة الرئيسية',
                  onTap: () => Navigator.pop(context),
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.book,
                  title: 'السيرة النبوية',
                  onTap: () => Navigator.pop(context),
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.format_quote,
                  title: 'الأحاديث النبوية',
                  onTap: () => Navigator.pop(context),
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.location_on,
                  title: 'الأماكن المقدسة',
                  onTap: () => Navigator.pop(context),
                ),

                const Divider(),

                // إعدادات الثيم
                _buildThemeSection(context),

                const Divider(),

                _buildDrawerItem(
                  context,
                  icon: Icons.info,
                  title: 'حول التطبيق',
                  onTap: () => _showAboutDialog(context),
                ),
              ],
            ),
          ),

          // تذييل الدرج
          _buildDrawerFooter(context),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: AppColors.primaryGradient,
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // شعار التطبيق
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: ColorUtils.withAlpha(Colors.black, 0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.mosque,
                  size: 30,
                  color: AppColors.primaryColor,
                ),
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              // اسم التطبيق
              Text(
                AppConstants.appName,
                style: GoogleFonts.amiri(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: AppConstants.smallPadding),

              // وصف التطبيق
              Text(
                'تطبيق تعليمي ديني جميل وأنيق',
                style: GoogleFonts.amiri(
                  fontSize: 14,
                  color: ColorUtils.withAlpha(Colors.white, 0.9),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Theme.of(context).iconTheme.color,
      ),
      title: Text(
        title,
        style: GoogleFonts.amiri(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppConstants.largePadding,
        vertical: AppConstants.smallPadding,
      ),
    );
  }

  Widget _buildThemeSection(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.largePadding,
                vertical: AppConstants.smallPadding,
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.palette,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),
                  Text(
                    'مظهر التطبيق',
                    style: GoogleFonts.amiri(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.largePadding * 2,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          Icons.light_mode,
                          size: 20,
                          color: !themeProvider.isDarkMode
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).iconTheme.color,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'نهاري',
                          style: GoogleFonts.amiri(
                            fontSize: 14,
                            color: !themeProvider.isDarkMode
                                ? Theme.of(context).primaryColor
                                : Theme.of(context).textTheme.bodyMedium?.color,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: themeProvider.isDarkMode,
                    onChanged: (value) => themeProvider.toggleTheme(),
                    activeColor: Theme.of(context).primaryColor,
                  ),
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          'ليلي',
                          style: GoogleFonts.amiri(
                            fontSize: 14,
                            color: themeProvider.isDarkMode
                                ? Theme.of(context).primaryColor
                                : Theme.of(context).textTheme.bodyMedium?.color,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.dark_mode,
                          size: 20,
                          color: themeProvider.isDarkMode
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).iconTheme.color,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDrawerFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          const Divider(),
          Text(
            'الإصدار ${AppConstants.appVersion}',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'جُعل بحب وعناية ❤️',
            style: GoogleFonts.amiri(
              fontSize: 12,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    Navigator.pop(context); // إغلاق الدرج أولاً

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'حول التطبيق',
          style: GoogleFonts.amiri(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppConstants.appName,
              style: GoogleFonts.amiri(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'تطبيق تعليمي ديني شامل يهدف إلى تعليم السيرة النبوية الشريفة والأحاديث النبوية والأماكن المقدسة بطريقة تفاعلية وجذابة.',
              style: GoogleFonts.amiri(
                fontSize: 16,
                height: 1.6,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'الإصدار: ${AppConstants.appVersion}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Container(
              padding: const EdgeInsets.all(AppConstants.smallPadding),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'جميع الحقوق محفوظة لمطور التطبيق\nوائل شايبي 2025',
                style: GoogleFonts.amiri(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'إغلاق',
              style: GoogleFonts.amiri(),
            ),
          ),
        ],
      ),
    );
  }


}
