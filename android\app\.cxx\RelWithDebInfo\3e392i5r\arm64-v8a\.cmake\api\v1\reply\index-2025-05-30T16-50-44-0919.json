{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/AndroidstudioSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/AndroidstudioSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/AndroidstudioSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/AndroidstudioSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-d346d5e4a9d668885fbe.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-8afaa3cf9d54388f89da.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-e115b5b4a8540214d3ac.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-8afaa3cf9d54388f89da.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-e115b5b4a8540214d3ac.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-d346d5e4a9d668885fbe.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}