import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:visibility_detector/visibility_detector.dart';

/// Widget للعد التصاعدي المتحرك
class AnimatedCounter extends StatefulWidget {
  final int targetNumber;
  final String label;
  final IconData icon;
  final Color color;
  final Duration duration;

  const AnimatedCounter({
    super.key,
    required this.targetNumber,
    required this.label,
    required this.icon,
    required this.color,
    this.duration = const Duration(milliseconds: 2000),
  });

  @override
  State<AnimatedCounter> createState() => _AnimatedCounterState();
}

class _AnimatedCounterState extends State<AnimatedCounter>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _hasAnimated = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0,
      end: widget.targetNumber.toDouble(),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _startAnimation() {
    if (!_hasAnimated) {
      _hasAnimated = true;
      _controller.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key('animated-counter-${widget.label}'),
      onVisibilityChanged: (visibilityInfo) {
        // بدء الأنيميشن عندما يكون 50% من الwidget مرئي
        if (visibilityInfo.visibleFraction > 0.5) {
          _startAnimation();
        }
      },
      child: Column(
        children: [
          // الأيقونة مع أنيميشن نبضة
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 + (_controller.value * 0.1),
                child: Icon(
                  widget.icon,
                  size: 24,
                  color: widget.color,
                ),
              );
            },
          ),
          
          const SizedBox(height: 8),
          
          // الرقم المتحرك
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Text(
                _animation.value.round().toString(),
                style: GoogleFonts.amiri(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: widget.color,
                ),
              );
            },
          ),
          
          const SizedBox(height: 4),
          
          // النص التوضيحي
          Text(
            widget.label,
            style: GoogleFonts.amiri(
              fontSize: 12,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).textTheme.bodySmall?.color
                  : Colors.grey[700],
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
