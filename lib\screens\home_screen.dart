import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';
import '../core/utils/color_utils.dart';
import '../widgets/section_card.dart';
import '../widgets/welcome_card.dart';

/// شاشة الصفحة الرئيسية
class HomeScreen extends StatelessWidget {
  final Function(int)? onTabChange;

  const HomeScreen({super.key, this.onTabChange});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة الترحيب
            const WelcomeCard(),

            const SizedBox(height: AppConstants.largePadding),

            // عنوان الأقسام
            Text(
              'أقسام التطبيق',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // شبكة الأقسام - تخطيط محسن للبطاقات الثلاث بأحجام متساوية
            Column(
              children: [
                // البطاقة الأولى - بنفس حجم البطاقات الأخرى
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 190, // ارتفاع ثابت لجميع البطاقات
                        child: SectionCard(
                          title: 'السيرة النبوية',
                          subtitle: 'حياة النبي محمد ﷺ',
                          icon: Icons.book,
                          color: AppColors.seerahSectionColor,
                          sectionType: 'seerah',
                          onTap: () => _navigateToSection(context, 1),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppConstants.defaultPadding),

                // البطاقتان الثانية والثالثة - جنباً إلى جنب بنفس الحجم
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 190, // نفس الارتفاع
                        child: SectionCard(
                          title: 'الأحاديث النبوية',
                          subtitle: 'أقوال النبي ﷺ',
                          icon: Icons.format_quote,
                          color: AppColors.hadithSectionColor,
                          sectionType: 'hadith',
                          onTap: () => _navigateToSection(context, 2),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                    Expanded(
                      child: SizedBox(
                        height: 190, // نفس الارتفاع
                        child: SectionCard(
                          title: 'الأماكن المقدسة',
                          subtitle: 'أماكن مهمة في السيرة',
                          icon: Icons.location_on,
                          color: AppColors.placesSectionColor,
                          sectionType: 'places',
                          onTap: () => _navigateToSection(context, 3),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: AppConstants.largePadding),

            // بطاقة إحصائيات سريعة
            _buildQuickStatsCard(context),

            const SizedBox(height: AppConstants.largePadding),

            // بطاقة آية أو حديث اليوم
            _buildDailyWisdomCard(context),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'محتوى التطبيق',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(context, '7', 'مراحل السيرة', Icons.timeline),
                _buildStatItem(context, '5', 'أحاديث نبوية', Icons.format_quote),
                _buildStatItem(context, '7', 'أماكن مقدسة', Icons.location_on),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String number, String label, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: ColorUtils.withLowAlpha(Theme.of(context).primaryColor),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).brightness == Brightness.dark
                ? AppColors.primaryLightColor
                : Theme.of(context).primaryColor,
            size: 24,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Text(
          number,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).brightness == Brightness.dark
                ? AppColors.primaryLightColor
                : Theme.of(context).primaryColor,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).textTheme.bodySmall?.color
                : Colors.grey[700], // لون أغمق وأوضح للوضع النهاري
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildDailyWisdomCard(BuildContext context) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppColors.primaryGradient.map((color) =>
              ColorUtils.withAlpha(color, 0.1)).toList(),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppColors.primaryLightColor
                      : Theme.of(context).primaryColor,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'حكمة اليوم',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? AppColors.primaryLightColor
                        : Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              '"إنما بُعثت لأتمم مكارم الأخلاق"',
              style: GoogleFonts.amiri(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                height: 1.8,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'رواه أحمد والبيهقي',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodySmall?.color
                    : Colors.grey[600], // لون أغمق للوضع النهاري
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToSection(BuildContext context, int sectionIndex) {
    // استخدام callback لتغيير التبويب بدلاً من التنقل المباشر
    if (onTabChange != null) {
      onTabChange!(sectionIndex);
    } else {
      // fallback للتنقل المباشر إذا لم يكن callback متاحاً
      switch (sectionIndex) {
        case 1:
          Navigator.pushNamed(context, '/seerah');
          break;
        case 2:
          Navigator.pushNamed(context, '/hadith');
          break;
        case 3:
          Navigator.pushNamed(context, '/places');
          break;
        default:
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('القسم غير متاح حالياً'),
              duration: Duration(seconds: 1),
            ),
          );
      }
    }
  }


}
