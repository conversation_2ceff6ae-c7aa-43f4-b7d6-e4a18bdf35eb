import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';
import '../core/utils/color_utils.dart';
import '../widgets/welcome_card.dart';

/// شاشة الصفحة الرئيسية
class HomeScreen extends StatefulWidget {
  final Function(int)? onTabChange;

  const HomeScreen({super.key, this.onTabChange});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد أنيميشن الظهور التدريجي
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // إعداد أنيميشن الانزلاق
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // بدء الأنيميشن
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة الترحيب مع أنيميشن
            FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: const WelcomeCard(),
              ),
            ),

            const SizedBox(height: AppConstants.largePadding),

            // عنوان الأقسام مع أنيميشن
            FadeTransition(
              opacity: _fadeAnimation,
              child: Text(
                'أقسام التطبيق',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // شبكة الأقسام مع أنيميشن متدرج
            _buildAnimatedSections(context),

            const SizedBox(height: AppConstants.largePadding),

            // بطاقة إحصائيات سريعة مع أنيميشن
            FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: _buildQuickStatsCard(context),
              ),
            ),

            const SizedBox(height: AppConstants.largePadding),

            // بطاقة آية أو حديث اليوم مع أنيميشن
            FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: _buildDailyWisdomCard(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedSections(BuildContext context) {
    return Column(
      children: [
        // البطاقة الأولى
        FadeTransition(
          opacity: _fadeAnimation,
          child: Row(
            children: [
              Expanded(
                child: SizedBox(
                  height: 190,
                  child: _buildAnimatedSectionCard(
                    context,
                    'السيرة النبوية',
                    'حياة النبي محمد ﷺ',
                    Icons.book,
                    AppColors.seerahSectionColor,
                    'seerah',
                    () => _navigateToSection(context, 1),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // البطاقتان الثانية والثالثة
        FadeTransition(
          opacity: _fadeAnimation,
          child: Row(
            children: [
              Expanded(
                child: SizedBox(
                  height: 190,
                  child: _buildAnimatedSectionCard(
                    context,
                    'الأحاديث النبوية',
                    'أقوال النبي ﷺ',
                    Icons.format_quote,
                    AppColors.hadithSectionColor,
                    'hadith',
                    () => _navigateToSection(context, 2),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: SizedBox(
                  height: 190,
                  child: _buildAnimatedSectionCard(
                    context,
                    'الأماكن المقدسة',
                    'أماكن مهمة في السيرة',
                    Icons.location_on,
                    AppColors.placesSectionColor,
                    'places',
                    () => _navigateToSection(context, 3),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // البطاقة الرابعة - الصحابة الكرام
        FadeTransition(
          opacity: _fadeAnimation,
          child: Row(
            children: [
              Expanded(
                child: SizedBox(
                  height: 190,
                  child: _buildAnimatedSectionCard(
                    context,
                    'الصحابة الكرام',
                    'أصحاب النبي ﷺ',
                    Icons.people,
                    AppColors.companionsSectionColor,
                    'companions',
                    () => _navigateToSection(context, 4),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAnimatedSectionCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    String sectionType,
    VoidCallback onTap,
  ) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          child: Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  ColorUtils.withAlpha(color, 0.1),
                  ColorUtils.withAlpha(color, 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              border: Border.all(
                color: ColorUtils.withAlpha(color, 0.2),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // الأيقونة
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: ColorUtils.withAlpha(color, 0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    icon,
                    size: 28,
                    color: color,
                  ),
                ),

                const SizedBox(height: 8),

                // العنوان
                Text(
                  title,
                  style: GoogleFonts.amiri(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.titleLarge?.color,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 4),

                // العنوان الفرعي
                Text(
                  subtitle,
                  style: GoogleFonts.amiri(
                    fontSize: 11,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Theme.of(context).textTheme.bodySmall?.color
                        : Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 6),

                // مؤشر الانتقال
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 3,
                  ),
                  decoration: BoxDecoration(
                    color: ColorUtils.withAlpha(color, 0.15),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'استكشف',
                        style: GoogleFonts.amiri(
                          fontSize: 9,
                          fontWeight: FontWeight.w600,
                          color: color,
                        ),
                      ),
                      const SizedBox(width: 3),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 9,
                        color: color,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStatsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'محتوى التطبيق',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(context, '8', 'مراحل السيرة', Icons.timeline),
                _buildStatItem(context, '15', 'أحاديث نبوية', Icons.format_quote),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(context, '7', 'أماكن مقدسة', Icons.location_on),
                _buildStatItem(context, '5', 'صحابة كرام', Icons.people),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String number, String label, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: ColorUtils.withLowAlpha(Theme.of(context).primaryColor),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).brightness == Brightness.dark
                ? AppColors.primaryLightColor
                : Theme.of(context).primaryColor,
            size: 24,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Text(
          number,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).brightness == Brightness.dark
                ? AppColors.primaryLightColor
                : Theme.of(context).primaryColor,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).textTheme.bodySmall?.color
                : Colors.grey[700], // لون أغمق وأوضح للوضع النهاري
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildDailyWisdomCard(BuildContext context) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppColors.primaryGradient.map((color) =>
              ColorUtils.withAlpha(color, 0.1)).toList(),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppColors.primaryLightColor
                      : Theme.of(context).primaryColor,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'حكمة اليوم',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? AppColors.primaryLightColor
                        : Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              '"إنما بُعثت لأتمم مكارم الأخلاق"',
              style: GoogleFonts.amiri(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                height: 1.8,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'رواه أحمد والبيهقي',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodySmall?.color
                    : Colors.grey[600], // لون أغمق للوضع النهاري
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToSection(BuildContext context, int sectionIndex) {
    // استخدام callback لتغيير التبويب بدلاً من التنقل المباشر
    if (widget.onTabChange != null) {
      widget.onTabChange!(sectionIndex);
    } else {
      // fallback للتنقل المباشر إذا لم يكن callback متاحاً
      switch (sectionIndex) {
        case 1:
          Navigator.pushNamed(context, '/seerah');
          break;
        case 2:
          Navigator.pushNamed(context, '/hadith');
          break;
        case 3:
          Navigator.pushNamed(context, '/places');
          break;
        case 4:
          // الصحابة الكرام - سيتم التنقل عبر التبويب
          break;
        default:
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('القسم غير متاح حالياً'),
              duration: Duration(seconds: 1),
            ),
          );
      }
    }
  }


}
