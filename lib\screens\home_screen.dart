import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';
import '../core/utils/color_utils.dart';
import '../widgets/section_card.dart';
import '../widgets/welcome_card.dart';

/// شاشة الصفحة الرئيسية
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة الترحيب
            const WelcomeCard(),

            const SizedBox(height: AppConstants.largePadding),

            // عنوان الأقسام
            Text(
              'أقسام التطبيق',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // شبكة الأقسام
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: AppConstants.defaultPadding,
              mainAxisSpacing: AppConstants.defaultPadding,
              childAspectRatio: 1.1,
              children: [
                SectionCard(
                  title: 'السيرة النبوية',
                  subtitle: 'حياة النبي محمد ﷺ',
                  icon: Icons.book,
                  color: AppColors.seerahSectionColor,
                  onTap: () => _navigateToSection(context, 1),
                ),
                SectionCard(
                  title: 'الأحاديث النبوية',
                  subtitle: 'أقوال النبي ﷺ',
                  icon: Icons.format_quote,
                  color: AppColors.hadithSectionColor,
                  onTap: () => _navigateToSection(context, 2),
                ),
                SectionCard(
                  title: 'الأماكن المقدسة',
                  subtitle: 'أماكن مهمة في السيرة',
                  icon: Icons.location_on,
                  color: AppColors.placesSectionColor,
                  onTap: () => _navigateToSection(context, 3),
                ),
                SectionCard(
                  title: 'حول التطبيق',
                  subtitle: 'معلومات التطبيق',
                  icon: Icons.info,
                  color: AppColors.aboutSectionColor,
                  onTap: () => _showAboutDialog(context),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.largePadding),

            // بطاقة إحصائيات سريعة
            _buildQuickStatsCard(context),

            const SizedBox(height: AppConstants.largePadding),

            // بطاقة آية أو حديث اليوم
            _buildDailyWisdomCard(context),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'محتوى التطبيق',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(context, '7', 'مراحل السيرة', Icons.timeline),
                _buildStatItem(context, '20+', 'حديث نبوي', Icons.format_quote),
                _buildStatItem(context, '10+', 'مكان مقدس', Icons.location_on),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String number, String label, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: ColorUtils.withLowAlpha(Theme.of(context).primaryColor),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Text(
          number,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildDailyWisdomCard(BuildContext context) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              ColorUtils.withLowAlpha(Theme.of(context).primaryColor),
              ColorUtils.withAlpha(Theme.of(context).primaryColor, 0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'حكمة اليوم',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              '"إنما بُعثت لأتمم مكارم الأخلاق"',
              style: GoogleFonts.amiri(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                height: 1.8,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'رواه أحمد والبيهقي',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToSection(BuildContext context, int sectionIndex) {
    // سيتم تنفيذ التنقل لاحقاً عبر تغيير الفهرس في MainScreen
    // يمكن استخدام callback أو state management لهذا الغرض
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم فتح القسم رقم $sectionIndex'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حول التطبيق'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppConstants.appName,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(AppConstants.appDescription),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'الإصدار: ${AppConstants.appVersion}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
