import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_icons.dart';
import '../core/utils/color_utils.dart';
import '../widgets/welcome_card.dart';
import '../widgets/animated_counter.dart';

/// شاشة الصفحة الرئيسية
class HomeScreen extends StatelessWidget {
  final Function(int)? onTabChange;

  const HomeScreen({super.key, this.onTabChange});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة الترحيب
            const WelcomeCard(),

            const SizedBox(height: AppConstants.largePadding),

            // عنوان الأقسام
            Text(
              'أقسام التطبيق',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // شبكة الأقسام
            _buildSections(context),

            const SizedBox(height: AppConstants.largePadding),

            // بطاقة إحصائيات سريعة
            _buildQuickStatsCard(context),

            const SizedBox(height: AppConstants.largePadding),

            // بطاقة آية أو حديث اليوم
            _buildDailyWisdomCard(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSections(BuildContext context) {
    return Column(
      children: [
        // البطاقة الأولى
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: 190,
                child: _buildSectionCard(
                  context,
                  'السيرة النبوية',
                  'حياة النبي محمد ﷺ',
                  AppIcons.seerahStages,
                  AppColors.seerahSectionColor,
                  'seerah',
                  () => _navigateToSection(context, 1),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // البطاقتان الثانية والثالثة
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: 190,
                child: _buildSectionCard(
                  context,
                  'الأحاديث النبوية',
                  'أقوال النبي ﷺ',
                  AppIcons.hadith,
                  AppColors.hadithSectionColor,
                  'hadith',
                  () => _navigateToSection(context, 2),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: SizedBox(
                height: 190,
                child: _buildSectionCard(
                  context,
                  'الأماكن المقدسة',
                  'أماكن مهمة في السيرة',
                  AppIcons.holyPlaces,
                  AppColors.placesSectionColor,
                  'places',
                  () => _navigateToSection(context, 3),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // البطاقة الرابعة - الصحابة الكرام
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: 190,
                child: _buildSectionCard(
                  context,
                  'الصحابة الكرام',
                  'أصحاب النبي ﷺ',
                  AppIcons.companionsCount,
                  AppColors.companionsSectionColor,
                  'companions',
                  () => _navigateToSection(context, 4),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSectionCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    String sectionType,
    VoidCallback onTap,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                ColorUtils.withAlpha(color, 0.1),
                ColorUtils.withAlpha(color, 0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(
              color: ColorUtils.withAlpha(color, 0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // الأيقونة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: ColorUtils.withAlpha(color, 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  icon,
                  size: 28,
                  color: color,
                ),
              ),

              const SizedBox(height: 8),

              // العنوان
              Text(
                title,
                style: GoogleFonts.amiri(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.titleLarge?.color,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 4),

              // العنوان الفرعي
              Text(
                subtitle,
                style: GoogleFonts.amiri(
                  fontSize: 11,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Theme.of(context).textTheme.bodySmall?.color
                      : Colors.grey[700],
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 6),

              // مؤشر الانتقال
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 3,
                ),
                decoration: BoxDecoration(
                  color: ColorUtils.withAlpha(color, 0.15),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'استكشف',
                      style: GoogleFonts.amiri(
                        fontSize: 9,
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                    const SizedBox(width: 3),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 9,
                      color: color,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStatsCard(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF424242)
                : const Color(0xFFE3F2FD),
            Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF303030)
                : const Color(0xFFE8F4FD),
          ],
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF616161)
              : const Color(0xFFBBDEFB),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'محتوى التطبيق',
            style: GoogleFonts.amiri(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.titleLarge?.color,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedCounter(
                targetNumber: 8,
                label: 'مراحل السيرة',
                icon: AppIcons.seerahStages,
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF64B5F6)
                    : const Color(0xFF1976D2),
              ),
              AnimatedCounter(
                targetNumber: 50,
                label: 'أحاديث نبوية',
                icon: AppIcons.hadithCount,
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF64B5F6)
                    : const Color(0xFF1976D2),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              AnimatedCounter(
                targetNumber: 7,
                label: 'أماكن مقدسة',
                icon: AppIcons.holyPlaces,
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF64B5F6)
                    : const Color(0xFF1976D2),
              ),
              AnimatedCounter(
                targetNumber: 30,
                label: 'صحابة كرام',
                icon: AppIcons.companionsCount,
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF64B5F6)
                    : const Color(0xFF1976D2),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDailyWisdomCard(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 2000),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.largePadding),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF2E7D32)
                    : const Color(0xFFE8F5E8),
                Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF1B5E20)
                    : const Color(0xFFEDF7ED),
              ],
            ),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF388E3C)
                  : const Color(0xFFC8E6C9),
              width: 1,
            ),
            boxShadow: [
              // الظل الأساسي
              BoxShadow(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF4CAF50).withValues(alpha: 0.4 * value)
                    : const Color(0xFF2E7D32).withValues(alpha: 0.3 * value),
                blurRadius: 15 * value,
                spreadRadius: 3 * value,
                offset: Offset(0, 5 * value),
              ),
              // الظل المشع الخارجي
              BoxShadow(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF81C784).withValues(alpha: 0.3 * value)
                    : const Color(0xFF4CAF50).withValues(alpha: 0.25 * value),
                blurRadius: 25 * value,
                spreadRadius: 6 * value,
                offset: const Offset(0, 0),
              ),
              // الظل المشع الداخلي المتحرك
              BoxShadow(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFFA5D6A7).withValues(alpha: 0.2 * value)
                    : const Color(0xFF66BB6A).withValues(alpha: 0.15 * value),
                blurRadius: 35 * value,
                spreadRadius: 10 * value,
                offset: const Offset(0, 0),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    AppIcons.dailyHadith,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFF81C784)
                        : const Color(0xFF388E3C),
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'حديث اليوم',
                    style: GoogleFonts.amiri(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.titleLarge?.color,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Text(
                '"إنما الأعمال بالنيات، وإنما لكل امرئ ما نوى"',
                style: GoogleFonts.amiri(
                  fontSize: 16,
                  fontStyle: FontStyle.italic,
                  height: 1.6,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'رواه البخاري ومسلم',
                style: GoogleFonts.amiri(
                  fontSize: 12,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF81C784)
                      : const Color(0xFF388E3C),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _navigateToSection(BuildContext context, int sectionIndex) {
    // استخدام callback لتغيير التبويب بدلاً من التنقل المباشر
    if (onTabChange != null) {
      onTabChange!(sectionIndex);
    } else {
      // fallback للتنقل المباشر إذا لم يكن callback متاحاً
      switch (sectionIndex) {
        case 1:
          Navigator.pushNamed(context, '/seerah');
          break;
        case 2:
          Navigator.pushNamed(context, '/hadith');
          break;
        case 3:
          Navigator.pushNamed(context, '/places');
          break;
        case 4:
          // الصحابة الكرام - سيتم التنقل عبر التبويب
          break;
        default:
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('القسم غير متاح حالياً'),
              duration: Duration(seconds: 1),
            ),
          );
      }
    }
  }
}
