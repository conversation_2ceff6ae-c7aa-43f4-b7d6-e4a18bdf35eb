/// نموذج المكان المقدس
class SacredPlace {
  final String id;
  final String name;
  final String description;
  final String historicalSignificance;
  final String location;
  final String imageAsset;
  final List<String> relatedEvents;
  final double? latitude;
  final double? longitude;
  final String currentStatus;

  const SacredPlace({
    required this.id,
    required this.name,
    required this.description,
    required this.historicalSignificance,
    required this.location,
    required this.imageAsset,
    required this.relatedEvents,
    this.latitude,
    this.longitude,
    required this.currentStatus,
  });

  factory SacredPlace.fromJson(Map<String, dynamic> json) {
    return SacredPlace(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      historicalSignificance: json['historicalSignificance'] as String,
      location: json['location'] as String,
      imageAsset: json['imageAsset'] as String,
      relatedEvents: List<String>.from(json['relatedEvents'] as List),
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
      currentStatus: json['currentStatus'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'historicalSignificance': historicalSignificance,
      'location': location,
      'imageAsset': imageAsset,
      'relatedEvents': relatedEvents,
      'latitude': latitude,
      'longitude': longitude,
      'currentStatus': currentStatus,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SacredPlace && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SacredPlace(id: $id, name: $name)';
  }
}
