import 'package:flutter/material.dart';

/// أيقونات التطبيق المخصصة والمحسنة
class AppIcons {
  // منع إنشاء instance من الكلاس
  AppIcons._();

  // ========== أيقونات الشاشة الرئيسية ==========
  
  /// أيقونة حديث اليوم
  static const IconData dailyHadith = Icons.auto_stories_rounded;
  
  /// أيقونة عدد الأحاديث
  static const IconData hadithCount = Icons.menu_book_rounded;
  
  /// أيقونة عدد الصحابة
  static const IconData companionsCount = Icons.groups_rounded;
  
  /// أيقونة الأماكن المقدسة
  static const IconData holyPlaces = Icons.location_on_rounded;
  
  /// أيقونة مراحل السيرة
  static const IconData seerahStages = Icons.timeline_rounded;

  // ========== أيقونات الأحاديث ==========
  
  /// أيقونة الحديث الشريف
  static const IconData hadith = Icons.format_quote_rounded;
  
  /// أيقونة الراوي
  static const IconData narrator = Icons.person_rounded;
  
  /// أيقونة المصدر
  static const IconData source = Icons.library_books_rounded;
  
  /// أيقونة التصنيف
  static const IconData category = Icons.label_rounded;
  
  /// أيقونة الشرح
  static const IconData explanation = Icons.lightbulb_rounded;
  
  /// أيقونة الكلمات المفتاحية
  static const IconData keywords = Icons.local_offer_rounded;
  
  /// أيقونة الحديث الصحيح
  static const IconData authentic = Icons.verified_rounded;

  // ========== أيقونات المفضلة ==========
  
  /// أيقونة القلب الممتلئ (مفضل)
  static const IconData favoriteSelected = Icons.favorite_rounded;
  
  /// أيقونة القلب الفارغ (غير مفضل)
  static const IconData favoriteUnselected = Icons.favorite_border_rounded;
  
  /// أيقونة قائمة المفضلة
  static const IconData favoritesList = Icons.bookmark_rounded;
  
  /// أيقونة مسح المفضلة
  static const IconData clearFavorites = Icons.clear_all_rounded;

  // ========== أيقونات الصحابة ==========
  
  /// أيقونة الصحابي
  static const IconData companion = Icons.account_circle_rounded;
  
  /// أيقونة الإنجازات
  static const IconData achievements = Icons.emoji_events_rounded;
  
  /// أيقونة الأقوال المشهورة
  static const IconData famousQuotes = Icons.format_quote_rounded;
  
  /// أيقونة العلاقة مع النبي ﷺ
  static const IconData relationWithProphet = Icons.favorite_rounded;
  
  /// أيقونة سنة الوفاة
  static const IconData deathYear = Icons.event_rounded;

  // ========== أيقونات السيرة النبوية ==========
  
  /// أيقونة المولد والطفولة
  static const IconData birth = Icons.child_care_rounded;
  
  /// أيقونة البعثة
  static const IconData prophethood = Icons.star_rounded;
  
  /// أيقونة الهجرة
  static const IconData hijra = Icons.flight_takeoff_rounded;
  
  /// أيقونة الغزوات
  static const IconData battles = Icons.security_rounded;
  
  /// أيقونة فتح مكة
  static const IconData conquest = Icons.flag_rounded;
  
  /// أيقونة حجة الوداع
  static const IconData farewell = Icons.mosque_rounded;

  // ========== أيقونات الأماكن المقدسة ==========
  
  /// أيقونة مكة المكرمة
  static const IconData mecca = Icons.mosque_rounded;
  
  /// أيقونة المدينة المنورة
  static const IconData medina = Icons.location_city_rounded;
  
  /// أيقونة المسجد الأقصى
  static const IconData alAqsa = Icons.domain_rounded;
  
  /// أيقونة الغار
  static const IconData cave = Icons.landscape_rounded;
  
  /// أيقونة الجبل
  static const IconData mountain = Icons.terrain_rounded;
  
  /// أيقونة البئر
  static const IconData well = Icons.water_drop_rounded;

  // ========== أيقونات التنقل والواجهة ==========
  
  /// أيقونة الرجوع
  static const IconData back = Icons.arrow_back_ios_rounded;
  
  /// أيقونة البحث
  static const IconData search = Icons.search_rounded;
  
  /// أيقونة الإعدادات
  static const IconData settings = Icons.settings_rounded;
  
  /// أيقونة المشاركة
  static const IconData share = Icons.share_rounded;
  
  /// أيقونة المعلومات
  static const IconData info = Icons.info_rounded;
  
  /// أيقونات الوضع الليلي/النهاري
  static const IconData lightMode = Icons.light_mode_rounded;
  static const IconData darkMode = Icons.dark_mode_rounded;

  // ========== أيقونات الحالات ==========
  
  /// أيقونة التحميل
  static const IconData loading = Icons.hourglass_empty_rounded;
  
  /// أيقونة الخطأ
  static const IconData error = Icons.error_rounded;
  
  /// أيقونة النجاح
  static const IconData success = Icons.check_circle_rounded;
  
  /// أيقونة التحذير
  static const IconData warning = Icons.warning_rounded;
  
  /// أيقونة فارغ
  static const IconData empty = Icons.inbox_rounded;

  // ========== دوال مساعدة للألوان ==========
  
  /// الحصول على لون الأيقونة حسب السياق
  static Color getIconColor(BuildContext context, {bool isSelected = false}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    if (isSelected) {
      return isDark ? const Color(0xFF81C784) : const Color(0xFF388E3C);
    }
    
    return isDark 
        ? Colors.grey[400]! 
        : Colors.grey[600]!;
  }
  
  /// الحصول على لون أيقونة المفضلة
  static Color getFavoriteColor(bool isFavorite) {
    return isFavorite ? Colors.red[400]! : Colors.grey[500]!;
  }
  
  /// الحصول على لون أيقونة الحديث الصحيح
  static Color getAuthenticColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF81C784)
        : const Color(0xFF2E7D32);
  }
}
