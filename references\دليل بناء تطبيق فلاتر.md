# دليل بناء تطبيق فلاتر - المرجع الشامل

## 📚 فهرس المحتويات

1. [أساسيات Flutter](#أساسيات-flutter)
2. [إعداد بيئة التطوير](#إعداد-بيئة-التطوير)
3. [هيكل المشروع المثالي](#هيكل-المشروع-المثالي)
4. [إدارة الحالة](#إدارة-الحالة)
5. [تطوير واجهات المستخدم](#تطوير-واجهات-المستخدم)
6. [التعامل مع البيانات](#التعامل-مع-البيانات)
7. [الأذونات والأمان](#الأذونات-والأمان)
8. [الخدمات الخلفية](#الخدمات-الخلفية)
9. [التطوير الأصلي](#التطوير-الأصلي)
10. [إدارة الملفات](#إدارة-الملفات)
11. [التكامل مع APIs](#التكامل-مع-apis)
12. [الاختبار والتشخيص](#الاختبار-والتشخيص)
13. [البناء والنشر](#البناء-والنشر)
14. [تحسين الأداء](#تحسين-الأداء)
15. [المشاكل الشائعة](#المشاكل-الشائعة)

---

## 🚀 أساسيات Flutter

### ما هو Flutter؟
Flutter هو إطار عمل مفتوح المصدر من Google لبناء تطبيقات متعددة المنصات باستخدام لغة Dart.

### المميزات الأساسية
- **تطوير متعدد المنصات**: Android, iOS, Web, Desktop
- **أداء عالي**: تجميع مباشر للكود الأصلي
- **Hot Reload**: تحديث فوري للتغييرات
- **واجهات جميلة**: نظام widgets متقدم
- **مجتمع نشط**: دعم قوي ومكتبات متنوعة

### متطلبات النظام
```bash
# Windows
- Windows 10 أو أحدث
- Git for Windows
- Android Studio / VS Code
- Chrome (للتطوير على الويب)

# macOS
- macOS 10.14 أو أحدث
- Xcode (لتطوير iOS)
- Android Studio / VS Code
- CocoaPods

# Linux
- Ubuntu 18.04+ أو توزيعة مماثلة
- Git
- Android Studio / VS Code
```

---

## ⚙️ إعداد بيئة التطوير

### 1. تثبيت Flutter SDK
```bash
# تحميل Flutter SDK
git clone https://github.com/flutter/flutter.git -b stable

# إضافة Flutter للمسار
export PATH="$PATH:`pwd`/flutter/bin"

# فحص التثبيت
flutter doctor
```

### 2. إعداد Android Studio
```bash
# تثبيت Android SDK
# تثبيت Android SDK Command-line Tools
# تثبيت Android SDK Build-Tools
# تثبيت Android Emulator

# قبول التراخيص
flutter doctor --android-licenses
```

### 3. إعداد VS Code (اختياري)
```json
// extensions.json
{
  "recommendations": [
    "dart-code.flutter",
    "dart-code.dart-code",
    "ms-vscode.vscode-json"
  ]
}
```

### 4. فحص البيئة
```bash
flutter doctor -v
flutter devices
flutter config --enable-web  # لتفعيل تطوير الويب
```

---

## 📁 هيكل المشروع المثالي

### الهيكل الأساسي
```
my_app/
├── lib/
│   ├── main.dart                 # نقطة البداية
│   ├── app.dart                  # تطبيق رئيسي
│   ├── core/                     # الوظائف الأساسية
│   │   ├── constants/            # الثوابت
│   │   ├── utils/               # الأدوات المساعدة
│   │   ├── errors/              # إدارة الأخطاء
│   │   └── network/             # إعدادات الشبكة
│   ├── features/                # الميزات
│   │   └── feature_name/
│   │       ├── data/            # طبقة البيانات
│   │       ├── domain/          # منطق العمل
│   │       └── presentation/    # واجهة المستخدم
│   ├── shared/                  # المكونات المشتركة
│   │   ├── widgets/             # Widgets مشتركة
│   │   ├── services/            # الخدمات
│   │   └── models/              # نماذج البيانات
│   └── config/                  # الإعدادات
├── assets/                      # الموارد
│   ├── images/
│   ├── fonts/
│   └── data/
├── test/                        # الاختبارات
├── android/                     # إعدادات Android
├── ios/                         # إعدادات iOS
└── pubspec.yaml                 # تبعيات المشروع
```

### pubspec.yaml المثالي
```yaml
name: my_app
description: A comprehensive Flutter application
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_bloc: ^8.1.3
  provider: ^6.1.2

  # Network & HTTP
  http: ^1.2.2
  dio: ^5.4.3+1

  # Local Storage
  shared_preferences: ^2.2.3
  flutter_secure_storage: ^9.2.2
  sqflite: ^2.3.3+1
  hive: ^2.2.3

  # UI & Navigation
  go_router: ^14.2.7
  flutter_screenutil: ^5.9.3
  cached_network_image: ^3.3.1

  # Utilities
  intl: ^0.19.0
  path_provider: ^2.1.3
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.0

  # Development
  flutter_lints: ^4.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.4
  build_runner: ^2.4.9

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/data/

  fonts:
    - family: Arabic
      fonts:
        - asset: assets/fonts/NotoSansArabic-Regular.ttf
        - asset: assets/fonts/NotoSansArabic-Bold.ttf
          weight: 700
```

---

## 🔄 إدارة الحالة

### 1. Provider (للمشاريع البسيطة)
```dart
// model
class CounterModel extends ChangeNotifier {
  int _count = 0;
  int get count => _count;

  void increment() {
    _count++;
    notifyListeners();
  }
}

// main.dart
void main() {
  runApp(
    ChangeNotifierProvider(
      create: (context) => CounterModel(),
      child: MyApp(),
    ),
  );
}

// widget
class CounterWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<CounterModel>(
      builder: (context, counter, child) {
        return Text('Count: ${counter.count}');
      },
    );
  }
}
```

### 2. BLoC (للمشاريع المعقدة)
```dart
// events
abstract class CounterEvent {}
class CounterIncremented extends CounterEvent {}
class CounterDecremented extends CounterEvent {}

// states
abstract class CounterState {}
class CounterInitial extends CounterState {}
class CounterValue extends CounterState {
  final int value;
  CounterValue(this.value);
}

// bloc
class CounterBloc extends Bloc<CounterEvent, CounterState> {
  CounterBloc() : super(CounterInitial()) {
    on<CounterIncremented>((event, emit) {
      final currentState = state;
      if (currentState is CounterValue) {
        emit(CounterValue(currentState.value + 1));
      } else {
        emit(CounterValue(1));
      }
    });
  }
}

// usage
BlocProvider(
  create: (context) => CounterBloc(),
  child: BlocBuilder<CounterBloc, CounterState>(
    builder: (context, state) {
      if (state is CounterValue) {
        return Text('Count: ${state.value}');
      }
      return Text('Count: 0');
    },
  ),
)
```

### 3. GetX (للمشاريع السريعة)
```dart
// controller
class CounterController extends GetxController {
  var count = 0.obs;

  void increment() => count++;
}

// usage
class CounterWidget extends StatelessWidget {
  final CounterController controller = Get.put(CounterController());

  @override
  Widget build(BuildContext context) {
    return Obx(() => Text('Count: ${controller.count}'));
  }
}
```

### 2. تصميم Responsive
```dart
// استخدام ScreenUtil
class ResponsiveWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: Size(375, 812), // حجم التصميم المرجعي
      builder: (context, child) {
        return Container(
          width: 200.w,  // عرض متجاوب
          height: 100.h, // ارتفاع متجاوب
          padding: EdgeInsets.all(16.r), // padding متجاوب
          child: Text(
            'نص متجاوب',
            style: TextStyle(fontSize: 16.sp), // خط متجاوب
          ),
        );
      },
    );
  }
}

// استخدام MediaQuery
class AdaptiveWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Container(
      width: isTablet ? screenWidth * 0.6 : screenWidth * 0.9,
      child: isTablet ? TabletLayout() : MobileLayout(),
    );
  }
}
```

### 3. ثيمات متقدمة
```dart
// light theme
final lightTheme = ThemeData(
  brightness: Brightness.light,
  primarySwatch: Colors.blue,
  fontFamily: 'Arabic',
  textTheme: TextTheme(
    headlineLarge: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: Colors.black87,
    ),
    bodyLarge: TextStyle(
      fontSize: 16,
      color: Colors.black87,
    ),
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: Colors.blue,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
  ),
);

// dark theme
final darkTheme = ThemeData(
  brightness: Brightness.dark,
  primarySwatch: Colors.blue,
  fontFamily: 'Arabic',
  scaffoldBackgroundColor: Colors.grey[900],
  textTheme: TextTheme(
    headlineLarge: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: Colors.white,
    ),
    bodyLarge: TextStyle(
      fontSize: 16,
      color: Colors.white70,
    ),
  ),
);
```

### 4. Widgets مخصصة
```dart
// زر مخصص
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;

  const CustomButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width ?? double.infinity,
      height: height ?? 50,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? Theme.of(context).primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: textColor ?? Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}

// حقل إدخال مخصص
class CustomTextField extends StatelessWidget {
  final String? hintText;
  final String? labelText;
  final TextEditingController? controller;
  final bool obscureText;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;
  final Widget? prefixIcon;
  final Widget? suffixIcon;

  const CustomTextField({
    Key? key,
    this.hintText,
    this.labelText,
    this.controller,
    this.obscureText = false,
    this.keyboardType,
    this.validator,
    this.prefixIcon,
    this.suffixIcon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      validator: validator,
      textDirection: TextDirection.rtl,
      decoration: InputDecoration(
        hintText: hintText,
        labelText: labelText,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).primaryColor,
            width: 2,
          ),
        ),
      ),
    );
  }
}
```

---

## 💾 التعامل مع البيانات

### 1. قاعدة البيانات المحلية (SQLite)
```dart
// database helper
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'app_database.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE users(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');
  }

  // CRUD operations
  Future<int> insertUser(Map<String, dynamic> user) async {
    final db = await database;
    return await db.insert('users', user);
  }

  Future<List<Map<String, dynamic>>> getUsers() async {
    final db = await database;
    return await db.query('users');
  }

  Future<int> updateUser(int id, Map<String, dynamic> user) async {
    final db = await database;
    return await db.update('users', user, where: 'id = ?', whereArgs: [id]);
  }

  Future<int> deleteUser(int id) async {
    final db = await database;
    return await db.delete('users', where: 'id = ?', whereArgs: [id]);
  }
}
```

### 2. التخزين المحلي (SharedPreferences)
```dart
// preferences service
class PreferencesService {
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // String values
  static Future<bool> setString(String key, String value) async {
    return await _prefs!.setString(key, value);
  }

  static String? getString(String key) {
    return _prefs!.getString(key);
  }

  // Bool values
  static Future<bool> setBool(String key, bool value) async {
    return await _prefs!.setBool(key, value);
  }

  static bool? getBool(String key) {
    return _prefs!.getBool(key);
  }

  // Int values
  static Future<bool> setInt(String key, int value) async {
    return await _prefs!.setInt(key, value);
  }

  static int? getInt(String key) {
    return _prefs!.getInt(key);
  }

  // Remove value
  static Future<bool> remove(String key) async {
    return await _prefs!.remove(key);
  }

  // Clear all
  static Future<bool> clear() async {
    return await _prefs!.clear();
  }
}
```

### 3. التخزين الآمن
```dart
// secure storage service
class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );

  static Future<void> write(String key, String value) async {
    await _storage.write(key: key, value: value);
  }

  static Future<String?> read(String key) async {
    return await _storage.read(key: key);
  }

  static Future<void> delete(String key) async {
    await _storage.delete(key: key);
  }

  static Future<void> deleteAll() async {
    await _storage.deleteAll();
  }

  // خاص للتوكنات
  static Future<void> saveToken(String token) async {
    await write('auth_token', token);
  }

  static Future<String?> getToken() async {
    return await read('auth_token');
  }

  static Future<void> deleteToken() async {
    await delete('auth_token');
  }
}
```

---

## 🔐 الأذونات والأمان

### 1. إدارة الأذونات في Android
```dart
// permission service
class PermissionService {
  // فحص الأذونات
  static Future<bool> hasStoragePermission() async {
    final status = await Permission.storage.status;
    return status.isGranted;
  }

  // طلب الأذونات
  static Future<bool> requestStoragePermission() async {
    final status = await Permission.storage.request();
    return status.isGranted;
  }

  // طلب أذونات متعددة
  static Future<Map<Permission, PermissionStatus>> requestMultiplePermissions() async {
    return await [
      Permission.storage,
      Permission.camera,
      Permission.microphone,
      Permission.location,
    ].request();
  }

  // فحص شامل للأذونات
  static Future<bool> checkAllPermissions() async {
    final permissions = await [
      Permission.storage,
      Permission.camera,
      Permission.microphone,
    ].request();

    return permissions.values.every((status) => status.isGranted);
  }

  // فتح إعدادات التطبيق
  static Future<void> openAppSettings() async {
    await openAppSettings();
  }
}

// استخدام الأذونات
class PermissionHandler {
  static Future<bool> handleCameraPermission() async {
    final status = await Permission.camera.status;

    if (status.isGranted) {
      return true;
    } else if (status.isDenied) {
      final result = await Permission.camera.request();
      return result.isGranted;
    } else if (status.isPermanentlyDenied) {
      // توجيه المستخدم لإعدادات التطبيق
      await PermissionService.openAppSettings();
      return false;
    }

    return false;
  }
}
```

### 2. أمان البيانات
```dart
// encryption service
class EncryptionService {
  static const String _key = 'your-32-character-secret-key-here';

  static String encrypt(String plainText) {
    final key = encrypt.Key.fromBase64(_key);
    final iv = encrypt.IV.fromSecureRandom(16);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));

    final encrypted = encrypter.encrypt(plainText, iv: iv);
    return '${iv.base64}:${encrypted.base64}';
  }

  static String decrypt(String encryptedText) {
    final parts = encryptedText.split(':');
    final iv = encrypt.IV.fromBase64(parts[0]);
    final encrypted = encrypt.Encrypted.fromBase64(parts[1]);

    final key = encrypt.Key.fromBase64(_key);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));

    return encrypter.decrypt(encrypted, iv: iv);
  }
}

// secure API service
class SecureApiService {
  static const String baseUrl = 'https://api.example.com';

  static Future<Map<String, String>> _getHeaders() async {
    final token = await SecureStorageService.getToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
    };
  }

  static Future<http.Response> secureGet(String endpoint) async {
    final headers = await _getHeaders();
    final response = await http.get(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
    );

    if (response.statusCode == 401) {
      // Token expired, refresh or logout
      await _handleUnauthorized();
    }

    return response;
  }

  static Future<void> _handleUnauthorized() async {
    await SecureStorageService.deleteToken();
    // Navigate to login screen
  }
}
```

---

## ⚙️ الخدمات الخلفية

### 1. Background Services أساسية
```dart
// background service
class BackgroundService {
  static Future<void> initialize() async {
    await AndroidAlarmManager.initialize();
  }

  static Future<void> startPeriodicTask() async {
    await AndroidAlarmManager.periodic(
      const Duration(minutes: 15),
      0, // unique id
      backgroundCallback,
      exact: true,
      wakeup: true,
    );
  }

  static Future<void> stopPeriodicTask() async {
    await AndroidAlarmManager.cancel(0);
  }
}

// background callback
@pragma('vm:entry-point')
void backgroundCallback() async {
  print('Background task executed');
  // تنفيذ المهام الخلفية هنا
}
```

### 2. Foreground Service (Android)
```kotlin
// BackgroundService.kt
class BackgroundService : Service() {
    companion object {
        private const val NOTIFICATION_ID = 1
        private const val CHANNEL_ID = "background_service_channel"
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())

        // بدء المهام الخلفية
        startBackgroundWork()

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Background Service",
                NotificationManager.IMPORTANCE_LOW
            )
            val manager = getSystemService(NotificationManager::class.java)
            manager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("App Running")
            .setContentText("Background service is active")
            .setSmallIcon(R.drawable.ic_notification)
            .build()
    }

    private fun startBackgroundWork() {
        // تنفيذ المهام الخلفية
    }
}
```

### 3. WorkManager للمهام المجدولة
```dart
// work manager service
class WorkManagerService {
  static Future<void> schedulePeriodicWork() async {
    await Workmanager().initialize(callbackDispatcher);

    await Workmanager().registerPeriodicTask(
      'periodic-task',
      'periodicTask',
      frequency: Duration(hours: 1),
      constraints: Constraints(
        networkType: NetworkType.connected,
        requiresBatteryNotLow: true,
      ),
    );
  }

  static Future<void> cancelAllTasks() async {
    await Workmanager().cancelAll();
  }
}

@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      case 'periodicTask':
        await performPeriodicTask();
        break;
    }
    return Future.value(true);
  });
}

Future<void> performPeriodicTask() async {
  // تنفيذ المهمة الدورية
  print('Periodic task executed');
}
```

---

## 📱 التطوير الأصلي (Native Development)

### 1. Platform Channels
```dart
// platform channel service
class PlatformChannelService {
  static const MethodChannel _channel = MethodChannel('com.example.app/native');

  // استدعاء دالة أصلية
  static Future<String> getNativeData() async {
    try {
      final result = await _channel.invokeMethod('getNativeData');
      return result as String;
    } on PlatformException catch (e) {
      print('Error: ${e.message}');
      return '';
    }
  }

  // إرسال بيانات للكود الأصلي
  static Future<bool> sendDataToNative(Map<String, dynamic> data) async {
    try {
      final result = await _channel.invokeMethod('sendData', data);
      return result as bool;
    } on PlatformException catch (e) {
      print('Error: ${e.message}');
      return false;
    }
  }

  // استقبال أحداث من الكود الأصلي
  static void listenToNativeEvents() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onNativeEvent':
          final data = call.arguments as Map<String, dynamic>;
          _handleNativeEvent(data);
          break;
      }
    });
  }

  static void _handleNativeEvent(Map<String, dynamic> data) {
    print('Native event received: $data');
  }
}
```

### 2. Android Native Code
```kotlin
// MainActivity.kt
class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example.app/native"

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "getNativeData" -> {
                        val data = getNativeData()
                        result.success(data)
                    }
                    "sendData" -> {
                        val data = call.arguments as Map<String, Any>
                        val success = sendDataToNative(data)
                        result.success(success)
                    }
                    else -> {
                        result.notImplemented()
                    }
                }
            }
    }

    private fun getNativeData(): String {
        // تنفيذ الكود الأصلي
        return "Native data from Android"
    }

    private fun sendDataToNative(data: Map<String, Any>): Boolean {
        // معالجة البيانات المرسلة من Flutter
        return true
    }
}
```

### 3. iOS Native Code
```swift
// AppDelegate.swift
import UIKit
import Flutter

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {

        let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
        let nativeChannel = FlutterMethodChannel(
            name: "com.example.app/native",
            binaryMessenger: controller.binaryMessenger
        )

        nativeChannel.setMethodCallHandler({
            (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in

            switch call.method {
            case "getNativeData":
                let data = self.getNativeData()
                result(data)
            case "sendData":
                if let args = call.arguments as? [String: Any] {
                    let success = self.sendDataToNative(data: args)
                    result(success)
                }
            default:
                result(FlutterMethodNotImplemented)
            }
        })

        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    private func getNativeData() -> String {
        return "Native data from iOS"
    }

    private func sendDataToNative(data: [String: Any]) -> Bool {
        // معالجة البيانات
        return true
    }
}
```

---

## 📂 إدارة الملفات والوسائط

### 1. الوصول للملفات
```dart
// file service
class FileService {
  // الحصول على مجلدات النظام
  static Future<Directory> getApplicationDocumentsDirectory() async {
    return await path_provider.getApplicationDocumentsDirectory();
  }

  static Future<Directory> getTemporaryDirectory() async {
    return await path_provider.getTemporaryDirectory();
  }

  static Future<Directory?> getExternalStorageDirectory() async {
    return await path_provider.getExternalStorageDirectory();
  }

  // قراءة ملف
  static Future<String> readFile(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      return await file.readAsString();
    }
    throw FileSystemException('File not found', filePath);
  }

  // كتابة ملف
  static Future<void> writeFile(String filePath, String content) async {
    final file = File(filePath);
    await file.writeAsString(content);
  }

  // نسخ ملف
  static Future<void> copyFile(String sourcePath, String destinationPath) async {
    final sourceFile = File(sourcePath);
    await sourceFile.copy(destinationPath);
  }

  // حذف ملف
  static Future<void> deleteFile(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      await file.delete();
    }
  }

  // فحص وجود ملف
  static Future<bool> fileExists(String filePath) async {
    final file = File(filePath);
    return await file.exists();
  }

  // الحصول على حجم الملف
  static Future<int> getFileSize(String filePath) async {
    final file = File(filePath);
    return await file.length();
  }
}
```

### 2. اختيار الملفات
```dart
// file picker service
class FilePickerService {
  // اختيار ملف واحد
  static Future<File?> pickSingleFile({
    List<String>? allowedExtensions,
    FileType type = FileType.any,
  }) async {
    final result = await FilePicker.platform.pickFiles(
      type: type,
      allowedExtensions: allowedExtensions,
      allowMultiple: false,
    );

    if (result != null && result.files.isNotEmpty) {
      return File(result.files.first.path!);
    }
    return null;
  }

  // اختيار ملفات متعددة
  static Future<List<File>> pickMultipleFiles({
    List<String>? allowedExtensions,
    FileType type = FileType.any,
  }) async {
    final result = await FilePicker.platform.pickFiles(
      type: type,
      allowedExtensions: allowedExtensions,
      allowMultiple: true,
    );

    if (result != null) {
      return result.files
          .where((file) => file.path != null)
          .map((file) => File(file.path!))
          .toList();
    }
    return [];
  }

  // اختيار صورة
  static Future<File?> pickImage({ImageSource source = ImageSource.gallery}) async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: source);

    if (pickedFile != null) {
      return File(pickedFile.path);
    }
    return null;
  }

  // اختيار فيديو
  static Future<File?> pickVideo({ImageSource source = ImageSource.gallery}) async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickVideo(source: source);

    if (pickedFile != null) {
      return File(pickedFile.path);
    }
    return null;
  }
}
```

### 3. معالجة الصور
```dart
// image processing service
class ImageProcessingService {
  // ضغط الصورة
  static Future<File> compressImage(File imageFile, {int quality = 85}) async {
    final dir = await getTemporaryDirectory();
    final targetPath = '${dir.path}/compressed_${DateTime.now().millisecondsSinceEpoch}.jpg';

    final compressedFile = await FlutterImageCompress.compressAndGetFile(
      imageFile.absolute.path,
      targetPath,
      quality: quality,
    );

    return File(compressedFile!.path);
  }

  // تغيير حجم الصورة
  static Future<File> resizeImage(File imageFile, {int width = 800, int height = 600}) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image != null) {
      final resized = img.copyResize(image, width: width, height: height);
      final dir = await getTemporaryDirectory();
      final targetPath = '${dir.path}/resized_${DateTime.now().millisecondsSinceEpoch}.jpg';

      final resizedFile = File(targetPath);
      await resizedFile.writeAsBytes(img.encodeJpg(resized));
      return resizedFile;
    }

    return imageFile;
  }

  // إضافة علامة مائية
  static Future<File> addWatermark(File imageFile, String watermarkText) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image != null) {
      img.drawString(image, img.arial_24, 10, 10, watermarkText);

      final dir = await getTemporaryDirectory();
      final targetPath = '${dir.path}/watermarked_${DateTime.now().millisecondsSinceEpoch}.jpg';

      final watermarkedFile = File(targetPath);
      await watermarkedFile.writeAsBytes(img.encodeJpg(image));
      return watermarkedFile;
    }

    return imageFile;
  }
}
```

---

## 🌐 التكامل مع APIs

### 1. HTTP Client متقدم
```dart
// api service
class ApiService {
  static const String baseUrl = 'https://api.example.com';
  static const Duration timeout = Duration(seconds: 30);

  static final Dio _dio = Dio(BaseOptions(
    baseUrl: baseUrl,
    connectTimeout: timeout,
    receiveTimeout: timeout,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  ));

  static void initialize() {
    // إضافة interceptors
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // إضافة التوكن للطلبات
        final token = await SecureStorageService.getToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          // Token expired, refresh or logout
          await _handleUnauthorized();
        }
        handler.next(error);
      },
    ));
  }

  // GET request
  static Future<Response<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      return await _dio.get<T>(endpoint, queryParameters: queryParameters);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // POST request
  static Future<Response<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      return await _dio.post<T>(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // رفع ملف
  static Future<Response> uploadFile(
    String endpoint,
    File file, {
    String fieldName = 'file',
    Map<String, dynamic>? additionalData,
  }) async {
    final formData = FormData.fromMap({
      fieldName: await MultipartFile.fromFile(file.path),
      ...?additionalData,
    });

    try {
      return await _dio.post(endpoint, data: formData);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  static Exception _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return Exception('Connection timeout');
      case DioExceptionType.receiveTimeout:
        return Exception('Receive timeout');
      case DioExceptionType.badResponse:
        return Exception('Server error: ${error.response?.statusCode}');
      default:
        return Exception('Network error: ${error.message}');
    }
  }

  static Future<void> _handleUnauthorized() async {
    await SecureStorageService.deleteToken();
    // Navigate to login screen
  }
}
```

### 2. نماذج البيانات
```dart
// base model
abstract class BaseModel {
  Map<String, dynamic> toJson();

  @override
  String toString() => toJson().toString();
}

// user model
class User extends BaseModel {
  final int id;
  final String name;
  final String email;
  final DateTime createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'created_at': createdAt.toIso8601String(),
    };
  }

  User copyWith({
    int? id,
    String? name,
    String? email,
    DateTime? createdAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

// API response wrapper
class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final List<String>? errors;

  ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.errors,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'],
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : json['data'],
      errors: json['errors']?.cast<String>(),
    );
  }
}
```

---

## 🧪 الاختبار والتشخيص

### 1. اختبارات الوحدة
```dart
// test/unit_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:my_app/services/api_service.dart';

// Mock classes
class MockApiService extends Mock implements ApiService {}

void main() {
  group('User Service Tests', () {
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
    });

    test('should return user when API call is successful', () async {
      // Arrange
      final userData = {'id': 1, 'name': 'Test User', 'email': '<EMAIL>'};
      when(mockApiService.get('/users/1')).thenAnswer(
        (_) async => Response(data: userData, statusCode: 200),
      );

      // Act
      final result = await UserService.getUser(1);

      // Assert
      expect(result, isA<User>());
      expect(result.name, 'Test User');
    });

    test('should throw exception when API call fails', () async {
      // Arrange
      when(mockApiService.get('/users/1')).thenThrow(Exception('Network error'));

      // Act & Assert
      expect(() => UserService.getUser(1), throwsException);
    });
  });
}
```

### 2. اختبارات الواجهة
```dart
// test/widget_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:my_app/widgets/custom_button.dart';

void main() {
  group('CustomButton Widget Tests', () {
    testWidgets('should display correct text', (WidgetTester tester) async {
      // Arrange
      const buttonText = 'Test Button';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: buttonText,
              onPressed: () {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(buttonText), findsOneWidget);
    });

    testWidgets('should call onPressed when tapped', (WidgetTester tester) async {
      // Arrange
      bool wasPressed = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: 'Test',
              onPressed: () => wasPressed = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(CustomButton));

      // Assert
      expect(wasPressed, true);
    });
  });
}
```

### 3. اختبارات التكامل
```dart
// integration_test/app_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:my_app/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('App Integration Tests', () {
    testWidgets('complete user flow test', (WidgetTester tester) async {
      // Start app
      app.main();
      await tester.pumpAndSettle();

      // Test login flow
      await tester.enterText(find.byKey(Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(Key('password_field')), 'password123');
      await tester.tap(find.byKey(Key('login_button')));
      await tester.pumpAndSettle();

      // Verify navigation to home screen
      expect(find.byKey(Key('home_screen')), findsOneWidget);
    });
  });
}
```

---

## 🔨 البناء والنشر

### 1. إعدادات البناء
```gradle
// android/app/build.gradle
android {
    compileSdkVersion 34

    defaultConfig {
        applicationId "com.example.myapp"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

### 2. أوامر البناء
```bash
# تنظيف المشروع
flutter clean

# تحديث التبعيات
flutter pub get

# تشغيل code generation
flutter packages pub run build_runner build

# بناء للتطوير
flutter build apk --debug
flutter build ios --debug

# بناء للإنتاج
flutter build apk --release
flutter build appbundle --release
flutter build ios --release

# بناء للويب
flutter build web --release
```

### 3. تحسين حجم APK
```yaml
# pubspec.yaml
flutter:
  assets:
    - assets/images/

  # تحسين الخطوط
  fonts:
    - family: Arabic
      fonts:
        - asset: assets/fonts/arabic.ttf
```

```gradle
// android/app/build.gradle
android {
    buildTypes {
        release {
            // تفعيل تحسين الكود
            minifyEnabled true
            shrinkResources true

            // إزالة الكود غير المستخدم
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt')
        }
    }
}
```

---

## ⚡ تحسين الأداء

### 1. تحسين الذاكرة
```dart
// استخدام const constructors
class MyWidget extends StatelessWidget {
  const MyWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Text('Static text'); // const للنصوص الثابتة
  }
}

// تجنب إنشاء objects في build method
class OptimizedWidget extends StatelessWidget {
  // إنشاء style خارج build method
  static const textStyle = TextStyle(fontSize: 16, color: Colors.black);

  @override
  Widget build(BuildContext context) {
    return Text('Optimized text', style: textStyle);
  }
}

// استخدام ListView.builder للقوائم الطويلة
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return ListTile(title: Text(items[index]));
  },
)
```

### 2. تحسين الصور
```dart
// استخدام cached_network_image
CachedNetworkImage(
  imageUrl: 'https://example.com/image.jpg',
  placeholder: (context, url) => CircularProgressIndicator(),
  errorWidget: (context, url, error) => Icon(Icons.error),
  memCacheWidth: 200, // تحديد عرض cache
  memCacheHeight: 200, // تحديد ارتفاع cache
)

// ضغط الصور المحلية
Image.asset(
  'assets/images/large_image.jpg',
  width: 200,
  height: 200,
  fit: BoxFit.cover,
  cacheWidth: 200, // ضغط في الذاكرة
  cacheHeight: 200,
)
```

### 3. تحسين البناء
```dart
// استخدام RepaintBoundary للعناصر المعقدة
RepaintBoundary(
  child: ComplexWidget(),
)

// استخدام AutomaticKeepAliveClientMixin للحفاظ على الحالة
class KeepAliveWidget extends StatefulWidget {
  @override
  _KeepAliveWidgetState createState() => _KeepAliveWidgetState();
}

class _KeepAliveWidgetState extends State<KeepAliveWidget>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // مهم للـ KeepAlive
    return ExpensiveWidget();
  }
}
```

---

## 🚨 المشاكل الشائعة وحلولها

### 1. مشاكل البناء
```bash
# مشكلة: Gradle build failed
# الحل:
flutter clean
flutter pub get
cd android && ./gradlew clean && cd ..
flutter build apk

# مشكلة: Version conflict
# الحل: تحديث pubspec.yaml
dependency_overrides:
  package_name: ^version
```

### 2. مشاكل الأذونات
```dart
// مشكلة: Permission denied
// الحل: فحص وطلب الأذونات
Future<bool> checkAndRequestPermission() async {
  final status = await Permission.storage.status;

  if (status.isDenied) {
    final result = await Permission.storage.request();
    return result.isGranted;
  }

  if (status.isPermanentlyDenied) {
    await openAppSettings();
    return false;
  }

  return status.isGranted;
}
```

### 3. مشاكل الأداء
```dart
// مشكلة: UI lag
// الحل: استخدام Isolates للعمليات الثقيلة
Future<String> heavyComputation(String data) async {
  return await compute(_processData, data);
}

String _processData(String data) {
  // عملية معقدة
  return processedData;
}
```

### 4. مشاكل الشبكة
```dart
// مشكلة: Network timeout
// الحل: إعداد timeout وretry
final dio = Dio(BaseOptions(
  connectTimeout: Duration(seconds: 30),
  receiveTimeout: Duration(seconds: 30),
));

// إضافة retry interceptor
dio.interceptors.add(RetryInterceptor(
  dio: dio,
  options: RetryOptions(
    retries: 3,
    retryInterval: Duration(seconds: 2),
  ),
));
```

---

## 📚 الخلاصة والنصائح النهائية

### أفضل الممارسات العامة
1. **اتبع مبادئ SOLID** في تصميم الكود
2. **استخدم dependency injection** لسهولة الاختبار
3. **اكتب اختبارات شاملة** للكود الحرج
4. **وثق الكود** بتعليقات واضحة
5. **استخدم Git** لإدارة الإصدارات
6. **راجع الكود** قبل النشر
7. **اختبر على أجهزة حقيقية** قبل الإطلاق

### نصائح للأداء
- استخدم `const` كلما أمكن
- تجنب العمليات الثقيلة في `build` method
- استخدم `ListView.builder` للقوائم الطويلة
- ضغط الصور وتحسين الأصول
- استخدم `Isolates` للعمليات المعقدة

### نصائح للأمان
- لا تخزن البيانات الحساسة في plain text
- استخدم HTTPS للاتصالات
- تشفير البيانات المحلية المهمة
- فحص الأذونات بانتظام
- تحديث التبعيات باستمرار

هذا الدليل يمثل مرجعاً شاملاً لتطوير تطبيقات Flutter احترافية. استخدمه كنقطة انطلاق وطوره حسب احتياجات مشروعك.

---

**© 2025 - دليل بناء تطبيق فلاتر - المرجع الشامل**
