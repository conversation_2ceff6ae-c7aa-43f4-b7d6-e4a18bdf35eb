import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';
import '../core/utils/color_utils.dart';
import '../data/places_data.dart';
import '../models/sacred_place.dart';

/// شاشة الأماكن المقدسة
class PlacesScreen extends StatelessWidget {
  const PlacesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مقدمة الأماكن
            _buildIntroCard(context),

            const SizedBox(height: AppConstants.largePadding),

            // عنوان الأماكن
            Text(
              'الأماكن المقدسة في السيرة النبوية',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // قائمة الأماكن
            ..._buildPlacesList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroCard(BuildContext context) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.largePadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              ColorUtils.withLowAlpha(AppColors.placesSectionColor),
              ColorUtils.withAlpha(AppColors.placesSectionColor, 0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: AppColors.placesSectionColor,
                  size: 28,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'الأماكن المقدسة',
                  style: GoogleFonts.amiri(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: AppColors.placesSectionColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'تعرف على الأماكن المقدسة والمهمة في حياة النبي محمد صلى الله عليه وسلم وأهميتها التاريخية والدينية.',
              style: GoogleFonts.amiri(
                fontSize: 16,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildPlacesList(BuildContext context) {
    final places = PlacesData.getSacredPlaces();
    return places.map((place) => _buildPlaceCard(context, place)).toList();
  }

  Widget _buildPlaceCard(BuildContext context, SacredPlace place) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Card(
        child: InkWell(
          onTap: () => _showPlaceDetails(context, place),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العنوان والأيقونة
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: ColorUtils.withLowAlpha(AppColors.placesSectionColor),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        _getPlaceIcon(place.id),
                        color: AppColors.placesSectionColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            place.name,
                            style: GoogleFonts.amiri(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: AppConstants.smallPadding),
                          Text(
                            place.description,
                            style: GoogleFonts.amiri(
                              fontSize: 14,
                              color: Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: Theme.of(context).iconTheme.color,
                      size: 16,
                    ),
                  ],
                ),

                const SizedBox(height: AppConstants.defaultPadding),

                // الحالة
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.defaultPadding,
                    vertical: AppConstants.smallPadding,
                  ),
                  decoration: BoxDecoration(
                    color: ColorUtils.withLowAlpha(AppColors.placesSectionColor),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    place.currentStatus,
                    style: GoogleFonts.amiri(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.placesSectionColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getPlaceIcon(String placeId) {
    switch (placeId) {
      case '1':
        return Icons.home;
      case '2':
        return Icons.mosque;
      case '3':
        return Icons.landscape;
      case '4':
        return Icons.terrain;
      case '5':
        return Icons.account_balance;
      case '6':
        return Icons.flag;
      case '7':
        return Icons.water_drop;
      default:
        return Icons.location_on;
    }
  }

  void _showPlaceDetails(BuildContext context, SacredPlace place) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(AppConstants.largePadding),
          ),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // المحتوى
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.largePadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // العنوان
                    Row(
                      children: [
                        Icon(
                          _getPlaceIcon(place.id),
                          color: AppColors.placesSectionColor,
                          size: 28,
                        ),
                        const SizedBox(width: AppConstants.smallPadding),
                        Expanded(
                          child: Text(
                            place.name,
                            style: GoogleFonts.amiri(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // الوصف
                    Text(
                      place.description,
                      style: GoogleFonts.amiri(
                        fontSize: 16,
                        color: AppColors.placesSectionColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),

                    const SizedBox(height: AppConstants.largePadding),

                    // الأهمية التاريخية
                    Text(
                      'الأهمية التاريخية والدينية',
                      style: GoogleFonts.amiri(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Text(
                      place.historicalSignificance,
                      style: GoogleFonts.amiri(
                        fontSize: 16,
                        height: 1.8,
                      ),
                    ),

                    const SizedBox(height: AppConstants.largePadding),

                    // الأحداث المرتبطة
                    Text(
                      'الأحداث المرتبطة بهذا المكان',
                      style: GoogleFonts.amiri(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    ...place.relatedEvents.map((event) =>
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Icon(
                              Icons.circle,
                              size: 8,
                              color: AppColors.placesSectionColor,
                            ),
                            const SizedBox(width: AppConstants.smallPadding),
                            Expanded(
                              child: Text(
                                event,
                                style: GoogleFonts.amiri(fontSize: 15),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const Spacer(),

                    // زر الإغلاق
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'إغلاق',
                          style: GoogleFonts.amiri(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
