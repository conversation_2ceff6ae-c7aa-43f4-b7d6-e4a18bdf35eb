import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';

/// شاشة الأماكن المقدسة
class PlacesScreen extends StatelessWidget {
  const PlacesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مقدمة الأماكن
            _buildIntroCard(context),
            
            const SizedBox(height: AppConstants.largePadding),

            // عنوان الأماكن
            Text(
              'الأماكن المقدسة في السيرة النبوية',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // قائمة الأماكن
            ..._buildPlacesList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroCard(BuildContext context) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.largePadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              AppColors.placesSectionColor.withOpacity(0.1),
              AppColors.placesSectionColor.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: AppColors.placesSectionColor,
                  size: 28,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'الأماكن المقدسة',
                  style: GoogleFonts.amiri(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: AppColors.placesSectionColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'تعرف على الأماكن المقدسة والمهمة في حياة النبي محمد صلى الله عليه وسلم وأهميتها التاريخية والدينية.',
              style: GoogleFonts.amiri(
                fontSize: 16,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildPlacesList(BuildContext context) {
    final places = [
      {
        'name': 'مكة المكرمة',
        'description': 'مسقط رأس النبي ﷺ ومكان الكعبة المشرفة',
        'significance': 'مكة هي أقدس مكان في الإسلام، حيث ولد النبي محمد صلى الله عليه وسلم، وفيها الكعبة المشرفة التي يتوجه إليها المسلمون في صلاتهم.',
        'events': ['ولادة النبي ﷺ', 'بداية الدعوة', 'فتح مكة'],
        'icon': Icons.home,
        'status': 'مدينة مقدسة نشطة',
      },
      {
        'name': 'المدينة المنورة',
        'description': 'مدينة الهجرة ومكان دفن النبي ﷺ',
        'significance': 'المدينة المنورة هي المدينة التي هاجر إليها النبي ﷺ وأسس فيها أول دولة إسلامية، وفيها المسجد النبوي الشريف.',
        'events': ['الهجرة النبوية', 'بناء المسجد النبوي', 'وفاة النبي ﷺ'],
        'icon': Icons.mosque,
        'status': 'مدينة مقدسة نشطة',
      },
      {
        'name': 'غار حراء',
        'description': 'مكان نزول الوحي الأول',
        'significance': 'غار حراء هو المكان الذي كان يتعبد فيه النبي ﷺ قبل البعثة، وفيه نزل عليه الوحي لأول مرة.',
        'events': ['نزول الوحي الأول', 'بداية النبوة'],
        'icon': Icons.landscape,
        'status': 'موقع تاريخي محفوظ',
      },
      {
        'name': 'غار ثور',
        'description': 'مكان اختباء النبي ﷺ وأبي بكر أثناء الهجرة',
        'significance': 'غار ثور هو المكان الذي اختبأ فيه النبي ﷺ وأبو بكر الصديق لثلاثة أيام أثناء الهجرة من مكة إلى المدينة.',
        'events': ['الهجرة النبوية', 'معجزة العنكبوت والحمامة'],
        'icon': Icons.terrain,
        'status': 'موقع تاريخي محفوظ',
      },
      {
        'name': 'الطائف',
        'description': 'مدينة زارها النبي ﷺ لدعوة أهلها',
        'significance': 'الطائف هي المدينة التي ذهب إليها النبي ﷺ لدعوة أهلها للإسلام، وواجه فيها صعوبات كبيرة.',
        'events': ['رحلة الطائف', 'دعوة ثقيف'],
        'icon': Icons.location_city,
        'status': 'مدينة تاريخية نشطة',
      },
      {
        'name': 'بدر',
        'description': 'موقع أول معركة كبرى في الإسلام',
        'significance': 'بدر هو موقع أول معركة كبرى بين المسلمين والمشركين، والتي انتصر فيها المسلمون رغم قلة عددهم.',
        'events': ['غزوة بدر الكبرى'],
        'icon': Icons.flag,
        'status': 'موقع تاريخي',
      },
      {
        'name': 'أحد',
        'description': 'موقع معركة أحد',
        'significance': 'جبل أحد وما حوله شهد معركة أحد التي تعلم منها المسلمون دروساً مهمة في الطاعة والانضباط.',
        'events': ['غزوة أحد'],
        'icon': Icons.landscape,
        'status': 'موقع تاريخي محفوظ',
      },
    ];

    return places.map((place) => _buildPlaceCard(context, place)).toList();
  }

  Widget _buildPlaceCard(BuildContext context, Map<String, dynamic> place) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Card(
        child: InkWell(
          onTap: () => _showPlaceDetails(context, place),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العنوان والأيقونة
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.placesSectionColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        place['icon'] as IconData,
                        color: AppColors.placesSectionColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            place['name'] as String,
                            style: GoogleFonts.amiri(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: AppConstants.smallPadding),
                          Text(
                            place['description'] as String,
                            style: GoogleFonts.amiri(
                              fontSize: 14,
                              color: Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: Theme.of(context).iconTheme.color,
                      size: 16,
                    ),
                  ],
                ),

                const SizedBox(height: AppConstants.defaultPadding),

                // الحالة
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.defaultPadding,
                    vertical: AppConstants.smallPadding,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.placesSectionColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    place['status'] as String,
                    style: GoogleFonts.amiri(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.placesSectionColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showPlaceDetails(BuildContext context, Map<String, dynamic> place) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(AppConstants.largePadding),
          ),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // المحتوى
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.largePadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // العنوان
                    Row(
                      children: [
                        Icon(
                          place['icon'] as IconData,
                          color: AppColors.placesSectionColor,
                          size: 28,
                        ),
                        const SizedBox(width: AppConstants.smallPadding),
                        Expanded(
                          child: Text(
                            place['name'] as String,
                            style: GoogleFonts.amiri(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // الوصف
                    Text(
                      place['description'] as String,
                      style: GoogleFonts.amiri(
                        fontSize: 16,
                        color: AppColors.placesSectionColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),

                    const SizedBox(height: AppConstants.largePadding),

                    // الأهمية التاريخية
                    Text(
                      'الأهمية التاريخية والدينية',
                      style: GoogleFonts.amiri(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Text(
                      place['significance'] as String,
                      style: GoogleFonts.amiri(
                        fontSize: 16,
                        height: 1.8,
                      ),
                    ),

                    const SizedBox(height: AppConstants.largePadding),

                    // الأحداث المرتبطة
                    Text(
                      'الأحداث المرتبطة بهذا المكان',
                      style: GoogleFonts.amiri(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    ...((place['events'] as List<String>).map((event) => 
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Icon(
                              Icons.circle,
                              size: 8,
                              color: AppColors.placesSectionColor,
                            ),
                            const SizedBox(width: AppConstants.smallPadding),
                            Expanded(
                              child: Text(
                                event,
                                style: GoogleFonts.amiri(fontSize: 15),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )),

                    const Spacer(),

                    // زر الإغلاق
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'إغلاق',
                          style: GoogleFonts.amiri(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
