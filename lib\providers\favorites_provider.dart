import 'package:flutter/foundation.dart';
import '../services/favorites_service.dart';

/// مزود حالة المفضلة
class FavoritesProvider extends ChangeNotifier {
  final FavoritesService _favoritesService = FavoritesService.instance;
  List<String> _favoriteIds = [];
  bool _isLoading = false;

  List<String> get favoriteIds => _favoriteIds;
  bool get isLoading => _isLoading;
  int get favoritesCount => _favoriteIds.length;

  /// تحميل المفضلة من التخزين المحلي
  Future<void> loadFavorites() async {
    _isLoading = true;
    notifyListeners();

    try {
      _favoriteIds = await _favoritesService.getFavoriteIds();
    } catch (e) {
      _favoriteIds = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// التحقق من وجود حديث في المفضلة
  bool isFavorite(String hadithId) {
    return _favoriteIds.contains(hadithId);
  }

  /// تبديل حالة المفضلة
  Future<bool> toggleFavorite(String hadithId) async {
    try {
      final success = await _favoritesService.toggleFavorite(hadithId);
      
      if (success) {
        if (_favoriteIds.contains(hadithId)) {
          _favoriteIds.remove(hadithId);
        } else {
          _favoriteIds.add(hadithId);
        }
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// إضافة للمفضلة
  Future<bool> addToFavorites(String hadithId) async {
    if (_favoriteIds.contains(hadithId)) return false;

    try {
      final success = await _favoritesService.addToFavorites(hadithId);
      
      if (success) {
        _favoriteIds.add(hadithId);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// إزالة من المفضلة
  Future<bool> removeFromFavorites(String hadithId) async {
    if (!_favoriteIds.contains(hadithId)) return false;

    try {
      final success = await _favoritesService.removeFromFavorites(hadithId);
      
      if (success) {
        _favoriteIds.remove(hadithId);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// مسح جميع المفضلة
  Future<bool> clearAllFavorites() async {
    try {
      final success = await _favoritesService.clearAllFavorites();
      
      if (success) {
        _favoriteIds.clear();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}
