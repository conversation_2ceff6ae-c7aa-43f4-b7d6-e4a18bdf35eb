import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../core/constants/app_constants.dart';
import '../core/constants/app_colors.dart';
import '../core/utils/color_utils.dart';
import '../data/companions_data.dart';
import '../models/companion.dart';

/// شاشة الصحابة الكرام
class CompanionsScreen extends StatelessWidget {
  const CompanionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final companions = CompanionsData.getCompanions();

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // شريط التطبيق المرن
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                'الصحابة الكرام',
                style: GoogleFonts.amiri(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: AppColors.companionsGradient,
                  ),
                ),
                child: const Center(
                  child: Icon(
                    Icons.people,
                    size: 80,
                    color: Colors.white70,
                  ),
                ),
              ),
            ),
          ),

          // قائمة الصحابة
          SliverPadding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final companion = companions[index];
                  return Padding(
                    padding: const EdgeInsets.only(
                      bottom: AppConstants.defaultPadding,
                    ),
                    child: _buildCompanionCard(context, companion),
                  );
                },
                childCount: companions.length,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompanionCard(BuildContext context, Companion companion) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: InkWell(
        onTap: () => _showCompanionDetails(context, companion),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                ColorUtils.withAlpha(AppColors.companionsSectionColor, 0.1),
                ColorUtils.withAlpha(AppColors.companionsSectionColor, 0.05),
              ],
            ),
          ),
          child: Row(
            children: [
              // أيقونة الصحابي
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: ColorUtils.withAlpha(AppColors.companionsSectionColor, 0.2),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(
                  Icons.person,
                  size: 30,
                  color: AppColors.companionsSectionColor,
                ),
              ),

              const SizedBox(width: AppConstants.defaultPadding),

              // معلومات الصحابي
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // الاسم
                    Text(
                      companion.name,
                      style: GoogleFonts.amiri(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.titleLarge?.color,
                      ),
                    ),

                    const SizedBox(height: 4),

                    // اللقب
                    Text(
                      companion.title,
                      style: GoogleFonts.amiri(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.companionsSectionColor,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // الوصف
                    Text(
                      companion.description,
                      style: GoogleFonts.amiri(
                        fontSize: 13,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).textTheme.bodySmall?.color
                            : Colors.grey[700],
                        height: 1.4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // سهم التفاصيل
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.companionsSectionColor,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCompanionDetails(BuildContext context, Companion companion) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(20),
            ),
          ),
          child: SingleChildScrollView(
            controller: scrollController,
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // مقبض السحب
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),

                const SizedBox(height: AppConstants.defaultPadding),

                // رأس البطاقة
                _buildCompanionHeader(context, companion),

                const SizedBox(height: AppConstants.largePadding),

                // السيرة المفصلة
                _buildDetailedBiography(context, companion),

                const SizedBox(height: AppConstants.largePadding),

                // الإنجازات
                _buildAchievements(context, companion),

                const SizedBox(height: AppConstants.largePadding),

                // الأقوال المشهورة
                _buildFamousQuotes(context, companion),

                const SizedBox(height: AppConstants.largePadding),

                // العلاقة مع النبي
                _buildRelationWithProphet(context, companion),

                const SizedBox(height: AppConstants.largePadding),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompanionHeader(BuildContext context, Companion companion) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: AppColors.companionsGradient.map((color) =>
            ColorUtils.withAlpha(color, 0.1)).toList(),
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: ColorUtils.withAlpha(AppColors.companionsSectionColor, 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الاسم الكامل
          Text(
            companion.fullName,
            style: GoogleFonts.amiri(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.titleLarge?.color,
            ),
          ),

          const SizedBox(height: 8),

          // اللقب
          Text(
            companion.title,
            style: GoogleFonts.amiri(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.companionsSectionColor,
            ),
          ),

          const SizedBox(height: 12),

          // سنة الوفاة
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
              const SizedBox(width: 8),
              Text(
                'توفي سنة ${companion.deathYear}م',
                style: GoogleFonts.amiri(
                  fontSize: 14,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedBiography(BuildContext context, Companion companion) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'السيرة المفصلة',
          style: GoogleFonts.amiri(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.companionsSectionColor,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey[800]
                : Colors.grey[50],
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
          child: Text(
            companion.detailedBiography,
            style: GoogleFonts.amiri(
              fontSize: 15,
              height: 1.8,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAchievements(BuildContext context, Companion companion) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أهم الإنجازات',
          style: GoogleFonts.amiri(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.companionsSectionColor,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        ...companion.achievements.map((achievement) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 6),
                width: 6,
                height: 6,
                decoration: BoxDecoration(
                  color: AppColors.companionsSectionColor,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  achievement,
                  style: GoogleFonts.amiri(
                    fontSize: 15,
                    height: 1.6,
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildFamousQuotes(BuildContext context, Companion companion) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أقوال مشهورة',
          style: GoogleFonts.amiri(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.companionsSectionColor,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        ...companion.famousQuotes.map((quote) => Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: ColorUtils.withAlpha(AppColors.companionsSectionColor, 0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(
              color: ColorUtils.withAlpha(AppColors.companionsSectionColor, 0.3),
              width: 1,
            ),
          ),
          child: Text(
            '"$quote"',
            style: GoogleFonts.amiri(
              fontSize: 15,
              fontStyle: FontStyle.italic,
              height: 1.6,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
        )),
      ],
    );
  }

  Widget _buildRelationWithProphet(BuildContext context, Companion companion) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: AppColors.companionsGradient.map((color) =>
            ColorUtils.withAlpha(color, 0.15)).toList(),
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: ColorUtils.withAlpha(AppColors.companionsSectionColor, 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.favorite,
                color: AppColors.companionsSectionColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'العلاقة مع النبي ﷺ',
                style: GoogleFonts.amiri(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.companionsSectionColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            companion.relationWithProphet,
            style: GoogleFonts.amiri(
              fontSize: 15,
              height: 1.6,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
        ],
      ),
    );
  }
}
