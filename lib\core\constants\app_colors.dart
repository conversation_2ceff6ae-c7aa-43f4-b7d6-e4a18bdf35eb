import 'package:flutter/material.dart';

/// ألوان التطبيق المخصصة
class AppColors {
  // الألوان الأساسية
  static const Color primaryColor = Color(0xFF2E7D32); // أخضر إسلامي هادئ
  static const Color primaryLightColor = Color(0xFF66BB6A);
  static const Color primaryDarkColor = Color(0xFF1B5E20);
  
  static const Color secondaryColor = Color(0xFF8D6E63); // بني ذهبي
  static const Color secondaryLightColor = Color(0xFFBCAAA4);
  static const Color secondaryDarkColor = Color(0xFF5D4037);

  // ألوان الخلفية - الوضع النهاري
  static const Color lightBackground = Color(0xFFFAFAFA);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color lightCardBackground = Color(0xFFF5F5F5);

  // ألوان الخلفية - الوضع الليلي
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkCardBackground = Color(0xFF2C2C2C);

  // ألوان النصوص - الوضع النهاري
  static const Color lightTextPrimary = Color(0xFF212121);
  static const Color lightTextSecondary = Color(0xFF757575);
  static const Color lightTextHint = Color(0xFFBDBDBD);

  // ألوان النصوص - الوضع الليلي
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFB3B3B3);
  static const Color darkTextHint = Color(0xFF666666);

  // ألوان إضافية
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color errorColor = Color(0xFFF44336);
  static const Color infoColor = Color(0xFF2196F3);

  // ألوان الظلال
  static const Color shadowColor = Color(0x1A000000);
  static const Color lightShadowColor = Color(0x0D000000);

  // ألوان التدرج
  static const List<Color> primaryGradient = [
    Color(0xFF2E7D32),
    Color(0xFF66BB6A),
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF8D6E63),
    Color(0xFFBCAAA4),
  ];

  static const List<Color> backgroundGradient = [
    Color(0xFFF8F9FA),
    Color(0xFFE9ECEF),
  ];

  // ألوان خاصة بالأقسام
  static const Color seerahSectionColor = Color(0xFF4CAF50);
  static const Color hadithSectionColor = Color(0xFF2196F3);
  static const Color placesSectionColor = Color(0xFFFF9800);
  static const Color aboutSectionColor = Color(0xFF9C27B0);

  // شفافية
  static const double lowOpacity = 0.1;
  static const double mediumOpacity = 0.3;
  static const double highOpacity = 0.7;
}
