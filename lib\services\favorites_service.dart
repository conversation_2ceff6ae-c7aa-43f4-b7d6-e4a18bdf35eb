import 'package:shared_preferences/shared_preferences.dart';

/// خدمة إدارة المفضلة
class FavoritesService {
  static const String _favoritesKey = 'favorite_hadiths';
  static FavoritesService? _instance;

  FavoritesService._();

  static FavoritesService get instance {
    _instance ??= FavoritesService._();
    return _instance!;
  }

  /// الحصول على قائمة المفضلة
  Future<List<String>> getFavoriteIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList(_favoritesKey) ?? [];
      return favoritesJson;
    } catch (e) {
      return [];
    }
  }

  /// إضافة حديث للمفضلة
  Future<bool> addToFavorites(String hadithId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await getFavoriteIds();

      if (!favorites.contains(hadithId)) {
        favorites.add(hadithId);
        await prefs.setStringList(_favoritesKey, favorites);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// إزالة حديث من المفضلة
  Future<bool> removeFromFavorites(String hadithId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await getFavoriteIds();

      if (favorites.contains(hadithId)) {
        favorites.remove(hadithId);
        await prefs.setStringList(_favoritesKey, favorites);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من وجود حديث في المفضلة
  Future<bool> isFavorite(String hadithId) async {
    try {
      final favorites = await getFavoriteIds();
      return favorites.contains(hadithId);
    } catch (e) {
      return false;
    }
  }

  /// تبديل حالة المفضلة
  Future<bool> toggleFavorite(String hadithId) async {
    try {
      final isFav = await isFavorite(hadithId);
      if (isFav) {
        return await removeFromFavorites(hadithId);
      } else {
        return await addToFavorites(hadithId);
      }
    } catch (e) {
      return false;
    }
  }

  /// الحصول على عدد المفضلة
  Future<int> getFavoritesCount() async {
    try {
      final favorites = await getFavoriteIds();
      return favorites.length;
    } catch (e) {
      return 0;
    }
  }

  /// مسح جميع المفضلة
  Future<bool> clearAllFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_favoritesKey);
      return true;
    } catch (e) {
      return false;
    }
  }
}
