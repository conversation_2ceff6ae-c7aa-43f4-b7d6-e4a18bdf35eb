import 'package:shared_preferences/shared_preferences.dart';

/// خدمة إدارة المفضلة
class FavoritesService {
  static const String _favoritesKey = 'favorite_hadiths';
  static FavoritesService? _instance;

  FavoritesService._();

  static FavoritesService get instance {
    _instance ??= FavoritesService._();
    return _instance!;
  }

  /// الحصول على قائمة المفضلة
  Future<List<String>> getFavoriteIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList(_favoritesKey) ?? [];
      return favoritesJson;
    } catch (e) {
      return [];
    }
  }

  /// إضافة حديث للمفضلة (مع منع التكرار)
  Future<bool> addToFavorites(String hadithId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await getFavoriteIds();

      // التحقق من عدم وجود الحديث مسبقاً
      if (favorites.contains(hadithId)) {
        return false; // الحديث موجود مسبقاً
      }

      // إضافة الحديث للقائمة
      final updatedFavorites = List<String>.from(favorites);
      updatedFavorites.add(hadithId);

      // حفظ القائمة المحدثة
      await prefs.setStringList(_favoritesKey, updatedFavorites);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إزالة حديث من المفضلة (مع التحقق من الوجود)
  Future<bool> removeFromFavorites(String hadithId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await getFavoriteIds();

      // التحقق من وجود الحديث في القائمة
      if (!favorites.contains(hadithId)) {
        return false; // الحديث غير موجود
      }

      // إنشاء قائمة جديدة بدون الحديث المحدد
      final updatedFavorites = List<String>.from(favorites);
      updatedFavorites.remove(hadithId);

      // حفظ القائمة المحدثة
      await prefs.setStringList(_favoritesKey, updatedFavorites);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من وجود حديث في المفضلة
  Future<bool> isFavorite(String hadithId) async {
    try {
      final favorites = await getFavoriteIds();
      return favorites.contains(hadithId);
    } catch (e) {
      return false;
    }
  }

  /// تبديل حالة المفضلة (مع ضمان عدم التكرار)
  Future<bool> toggleFavorite(String hadithId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await getFavoriteIds();
      final updatedFavorites = List<String>.from(favorites);

      // التحقق من الحالة الحالية وتبديلها
      if (updatedFavorites.contains(hadithId)) {
        // إزالة من المفضلة
        updatedFavorites.remove(hadithId);
      } else {
        // إضافة للمفضلة (مع التأكد من عدم التكرار)
        if (!updatedFavorites.contains(hadithId)) {
          updatedFavorites.add(hadithId);
        }
      }

      // حفظ القائمة المحدثة
      await prefs.setStringList(_favoritesKey, updatedFavorites);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على عدد المفضلة
  Future<int> getFavoritesCount() async {
    try {
      final favorites = await getFavoriteIds();
      return favorites.length;
    } catch (e) {
      return 0;
    }
  }

  /// مسح جميع المفضلة
  Future<bool> clearAllFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_favoritesKey);
      return true;
    } catch (e) {
      return false;
    }
  }
}
