/// نموذج مرحلة من مراحل السيرة النبوية
class SeerahStage {
  final String id;
  final String title;
  final String subtitle;
  final String description;
  final String detailedContent;
  final String imageAsset;
  final DateTime? date;
  final String location;
  final List<String> keyPoints;
  final List<String> lessons;

  const SeerahStage({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.detailedContent,
    required this.imageAsset,
    this.date,
    required this.location,
    required this.keyPoints,
    required this.lessons,
  });

  factory SeerahStage.fromJson(Map<String, dynamic> json) {
    return SeerahStage(
      id: json['id'] as String,
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      description: json['description'] as String,
      detailedContent: json['detailedContent'] as String,
      imageAsset: json['imageAsset'] as String,
      date: json['date'] != null ? DateTime.parse(json['date']) : null,
      location: json['location'] as String,
      keyPoints: List<String>.from(json['keyPoints'] as List),
      lessons: List<String>.from(json['lessons'] as List),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'description': description,
      'detailedContent': detailedContent,
      'imageAsset': imageAsset,
      'date': date?.toIso8601String(),
      'location': location,
      'keyPoints': keyPoints,
      'lessons': lessons,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SeerahStage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SeerahStage(id: $id, title: $title)';
  }
}
