/// نموذج بيانات الصحابي
class Companion {
  final String id;
  final String name;
  final String fullName;
  final String title;
  final String description;
  final String detailedBiography;
  final List<String> achievements;
  final List<String> famousQuotes;
  final String relationWithProphet;
  final int deathYear;
  final String imageAsset;

  const Companion({
    required this.id,
    required this.name,
    required this.fullName,
    required this.title,
    required this.description,
    required this.detailedBiography,
    required this.achievements,
    required this.famousQuotes,
    required this.relationWithProphet,
    required this.deathYear,
    required this.imageAsset,
  });

  /// تحويل من JSON
  factory Companion.fromJson(Map<String, dynamic> json) {
    return Companion(
      id: json['id'] as String,
      name: json['name'] as String,
      fullName: json['fullName'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      detailedBiography: json['detailedBiography'] as String,
      achievements: List<String>.from(json['achievements'] as List),
      famousQuotes: List<String>.from(json['famousQuotes'] as List),
      relationWithProphet: json['relationWithProphet'] as String,
      deathYear: json['deathYear'] as int,
      imageAsset: json['imageAsset'] as String,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'fullName': fullName,
      'title': title,
      'description': description,
      'detailedBiography': detailedBiography,
      'achievements': achievements,
      'famousQuotes': famousQuotes,
      'relationWithProphet': relationWithProphet,
      'deathYear': deathYear,
      'imageAsset': imageAsset,
    };
  }

  /// نسخ مع تعديل
  Companion copyWith({
    String? id,
    String? name,
    String? fullName,
    String? title,
    String? description,
    String? detailedBiography,
    List<String>? achievements,
    List<String>? famousQuotes,
    String? relationWithProphet,
    int? deathYear,
    String? imageAsset,
  }) {
    return Companion(
      id: id ?? this.id,
      name: name ?? this.name,
      fullName: fullName ?? this.fullName,
      title: title ?? this.title,
      description: description ?? this.description,
      detailedBiography: detailedBiography ?? this.detailedBiography,
      achievements: achievements ?? this.achievements,
      famousQuotes: famousQuotes ?? this.famousQuotes,
      relationWithProphet: relationWithProphet ?? this.relationWithProphet,
      deathYear: deathYear ?? this.deathYear,
      imageAsset: imageAsset ?? this.imageAsset,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Companion && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Companion(id: $id, name: $name, title: $title)';
  }
}
