import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../models/hadith.dart';
import '../data/hadith_data.dart';
import '../data/additional_hadith_data.dart';
import '../data/more_hadith_data.dart';
import '../data/final_hadith_data.dart';
import '../providers/favorites_provider.dart';
import '../widgets/hadith_card.dart';
import '../core/constants/app_constants.dart';
import 'hadith_detail_screen.dart';

/// شاشة المفضلة
class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  List<Hadith> _allHadiths = [];

  @override
  void initState() {
    super.initState();
    _loadAllHadiths();
  }

  void _loadAllHadiths() {
    _allHadiths = [
      ...HadithData.getHadiths(),
      ...AdditionalHadithData.getAdditionalHadiths(),
      ...MoreHadithData.getMoreHadiths(),
      ...FinalHadithData.getFinalHadiths(),
    ];
  }

  List<Hadith> _getFavoriteHadiths(List<String> favoriteIds) {
    return _allHadiths.where((hadith) => favoriteIds.contains(hadith.id)).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الأحاديث المفضلة',
          style: GoogleFonts.amiri(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          Consumer<FavoritesProvider>(
            builder: (context, favoritesProvider, child) {
              if (favoritesProvider.favoritesCount > 0) {
                return IconButton(
                  onPressed: () => _showClearAllDialog(context, favoritesProvider),
                  icon: const Icon(Icons.clear_all),
                  tooltip: 'مسح جميع المفضلة',
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Consumer<FavoritesProvider>(
        builder: (context, favoritesProvider, child) {
          if (favoritesProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          final favoriteHadiths = _getFavoriteHadiths(favoritesProvider.favoriteIds);

          if (favoriteHadiths.isEmpty) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              // إحصائيات المفضلة
              Container(
                width: double.infinity,
                margin: const EdgeInsets.all(AppConstants.defaultPadding),
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFF2E7D32)
                          : const Color(0xFFE8F5E8),
                      Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFF1B5E20)
                          : const Color(0xFFEDF7ED),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.favorite,
                      color: Colors.red[400],
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'لديك ${favoriteHadiths.length} حديث في المفضلة',
                      style: GoogleFonts.amiri(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.titleMedium?.color,
                      ),
                    ),
                  ],
                ),
              ),

              // قائمة الأحاديث المفضلة
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                  itemCount: favoriteHadiths.length,
                  itemBuilder: (context, index) {
                    final hadith = favoriteHadiths[index];
                    return HadithCard(
                      hadith: hadith,
                      onTap: () => _navigateToHadithDetail(hadith),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 80,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey[600]
                : Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'لا توجد أحاديث مفضلة',
            style: GoogleFonts.amiri(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.titleMedium?.color,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'ابدأ بإضافة الأحاديث التي تحبها للمفضلة',
            style: GoogleFonts.amiri(
              fontSize: 14,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[400]
                  : Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.format_quote),
            label: Text(
              'تصفح الأحاديث',
              style: GoogleFonts.amiri(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToHadithDetail(Hadith hadith) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithDetailScreen(hadith: hadith),
      ),
    );
  }

  void _showClearAllDialog(BuildContext context, FavoritesProvider favoritesProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'مسح جميع المفضلة',
          style: GoogleFonts.amiri(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من أنك تريد مسح جميع الأحاديث المفضلة؟',
          style: GoogleFonts.amiri(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.amiri(),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await favoritesProvider.clearAllFavorites();
              
              if (success && context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'تم مسح جميع المفضلة بنجاح',
                      style: GoogleFonts.amiri(),
                    ),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'مسح الكل',
              style: GoogleFonts.amiri(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}
