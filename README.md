# تطبيق سيرة النبي محمد ﷺ

تطبيق جوال تعليمي ديني جميل وأنيق مطور باستخدام Flutter، يهدف إلى تقديم سيرة النبي محمد صلى الله عليه وسلم بطريقة سهلة وجذابة ومريحة نفسياً.

## 🌟 المميزات الرئيسية

### 📚 المحتوى التعليمي
- **السيرة النبوية**: 7 مراحل مفصلة من حياة النبي ﷺ
- **الأحاديث النبوية**: مجموعة مختارة من الأحاديث مع شروحات مبسطة
- **الأماكن المقدسة**: الأماكن المهمة في حياة النبي ﷺ
- **محتوى موثوق**: معلومات دقيقة من مصادر معتمدة

### 🎨 التصميم والواجهة
- **تصميم أنيق**: واجهة جميلة ومريحة نفسياً
- **دعم الوضع الليلي**: تبديل سلس بين الوضع النهاري والليلي
- **دعم اللغة العربية**: اتجاه RTL وخطوط عربية جميلة
- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات

### ⚡ الأداء والتجربة
- **سلاسة في الأداء**: تأثيرات انتقال سلسة
- **سهولة الاستخدام**: واجهة بديهية لجميع الفئات العمرية
- **ألوان مريحة**: ألوان هادئة ومتناسقة تبعث على الراحة

## 🛠️ التقنيات المستخدمة

- **Flutter**: إطار العمل الرئيسي
- **Dart**: لغة البرمجة
- **Provider**: إدارة الحالة
- **Google Fonts**: الخطوط العربية الجميلة
- **Material Design 3**: نظام التصميم
- **SharedPreferences**: التخزين المحلي

## 📱 الأقسام الرئيسية

1. **الصفحة الرئيسية**: نظرة عامة وإحصائيات سريعة
2. **السيرة النبوية**: مراحل حياة النبي ﷺ بالتفصيل
3. **الأحاديث النبوية**: أحاديث مختارة مع الشروحات
4. **الأماكن المقدسة**: الأماكن المهمة في السيرة النبوية

## 🚀 كيفية تشغيل التطبيق

### المتطلبات الأساسية
- Flutter SDK (3.7.0 أو أحدث)
- Dart SDK
- Android Studio أو VS Code
- Chrome (للتطوير على الويب)

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd pb
```

2. **تحميل التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
# للويب
flutter run -d chrome

# للأندرويد
flutter run -d android

# لـ iOS
flutter run -d ios
```

## 📁 هيكل المشروع

```
lib/
├── core/                    # الوظائف الأساسية
│   ├── constants/          # الثوابت والألوان
│   └── theme/              # إعدادات الثيمات
├── models/                 # نماذج البيانات
├── providers/              # مزودي الحالة
├── screens/                # الشاشات الرئيسية
├── widgets/                # الويدجت المخصصة
└── main.dart               # نقطة البداية
```

## 🎯 الميزات المستقبلية

- [ ] وظيفة البحث في المحتوى
- [ ] إضافة المفضلة
- [ ] الإشعارات اليومية
- [ ] المزيد من الأحاديث والمحتوى

## 📄 الترخيص

هذا المشروع مطور لأغراض تعليمية ودينية. جميع المحتويات مأخوذة من مصادر موثوقة ومعتمدة.

## 🤝 المساهمة

نرحب بالمساهمات لتحسين التطبيق. يرجى فتح issue أو pull request.

---

**جُعل بحب وعناية ❤️**
