import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// مزود المفضلة للتطبيق مبسط
class FavoritesProvider extends ChangeNotifier {
  List<String> _favorites = [];
  bool _isInitialized = false;

  List<String> get favorites => _favorites;
  bool get isInitialized => _isInitialized;

  /// تهيئة المفضلة من التخزين المحلي
  Future<void> loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _favorites = prefs.getStringList('favorites') ?? [];
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      _favorites = [];
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// إضافة عنصر للمفضلة
  Future<void> addToFavorites(String item) async {
    if (!_favorites.contains(item)) {
      _favorites.add(item);
      await _saveFavorites();
      notifyListeners();
    }
  }

  /// إزالة عنصر من المفضلة
  Future<void> removeFromFavorites(String item) async {
    if (_favorites.contains(item)) {
      _favorites.remove(item);
      await _saveFavorites();
      notifyListeners();
    }
  }

  /// التحقق من وجود عنصر في المفضلة
  bool isFavorite(String item) {
    return _favorites.contains(item);
  }

  /// حفظ المفضلة في التخزين المحلي
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('favorites', _favorites);
    } catch (e) {
      // تجاهل الخطأ في حفظ المفضلة
    }
  }
}
