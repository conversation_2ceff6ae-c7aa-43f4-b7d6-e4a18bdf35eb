import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/constants/app_constants.dart';
import '../core/utils/color_utils.dart';

import '../providers/theme_provider.dart';
import '../widgets/app_drawer.dart';
import 'home_screen.dart';
import 'seerah_screen.dart';
import 'hadith_screen.dart';
import 'places_screen.dart';

/// الشاشة الرئيسية التي تحتوي على شريط التبويب السفلي
class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const SeerahScreen(),
    const HadithScreen(),
    const PlacesScreen(),
  ];

  final List<BottomNavigationBarItem> _bottomNavItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.home_rounded),
      activeIcon: Icon(Icons.home),
      label: 'الرئيسية',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.book_rounded),
      activeIcon: Icon(Icons.book),
      label: 'السيرة النبوية',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.format_quote_rounded),
      activeIcon: Icon(Icons.format_quote),
      label: 'الأحاديث',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.location_on_rounded),
      activeIcon: Icon(Icons.location_on),
      label: 'الأماكن المقدسة',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle()),
        centerTitle: true,
        elevation: 0,
        actions: [
          // زر تبديل الثيم
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return IconButton(
                icon: Icon(themeProvider.currentThemeIcon),
                onPressed: () => themeProvider.toggleTheme(),
                tooltip: themeProvider.currentThemeName,
              );
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: AnimatedSwitcher(
        duration: AppConstants.animationDuration,
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.1, 0),
                end: Offset.zero,
              ).animate(animation),
              child: child,
            ),
          );
        },
        child: IndexedStack(
          key: ValueKey<int>(_currentIndex),
          index: _currentIndex,
          children: _screens,
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: ColorUtils.withLowAlpha(Theme.of(context).shadowColor),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: _onTabTapped,
          items: _bottomNavItems,
          type: BottomNavigationBarType.fixed,
          selectedFontSize: 12,
          unselectedFontSize: 10,
          elevation: 0,
        ),
      ),
    );
  }

  String _getAppBarTitle() {
    switch (_currentIndex) {
      case 0:
        return AppConstants.homeSection;
      case 1:
        return AppConstants.seerahSection;
      case 2:
        return AppConstants.hadithSection;
      case 3:
        return AppConstants.placesSection;
      default:
        return AppConstants.appName;
    }
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }
}
